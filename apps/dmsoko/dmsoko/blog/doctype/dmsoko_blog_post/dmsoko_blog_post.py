# Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.model.document import Document
from frappe.utils import cstr, get_url, strip_html_tags
from frappe.website.utils import get_comment_list
import re


class DMSOKOBlogPost(Document):
	def validate(self):
		self.validate_route()
		self.set_intro()
		self.set_read_time()
		
	def before_save(self):
		if self.content_type == "Rich Text":
			self.content_html = self.content
		elif self.content_type == "Markdown":
			self.content_html = frappe.utils.md_to_html(self.content)
		elif self.content_type == "HTML":
			self.content_html = self.content
			
	def validate_route(self):
		if not self.route:
			self.route = self.scrub_title()
		
		# Check if route already exists
		if frappe.db.exists("DMSOKO Blog Post", {"route": self.route, "name": ["!=", self.name]}):
			frappe.throw(_("A blog post with this route already exists"))
			
	def scrub_title(self):
		"""Generate URL-friendly route from title"""
		route = re.sub(r'[^\w\s-]', '', self.title.lower())
		route = re.sub(r'[-\s]+', '-', route)
		return route.strip('-')
		
	def set_intro(self):
		if not self.blog_intro and self.content:
			content = strip_html_tags(self.content)
			self.blog_intro = content[:200] + "..." if len(content) > 200 else content
			
	def set_read_time(self):
		if not self.read_time and self.content:
			# Estimate reading time based on word count (average 200 words per minute)
			content = strip_html_tags(self.content)
			word_count = len(content.split())
			self.read_time = max(1, round(word_count / 200))
			
	def get_context(self, context):
		"""Set context for web view"""
		context.no_cache = 1
		context.show_sidebar = True
		
		# Get related posts
		context.related_posts = self.get_related_posts()
		
		# Get comments if enabled
		if not self.disable_comments:
			context.comment_list = get_comment_list(self.doctype, self.name)
			
		return context
		
	def get_related_posts(self, limit=3):
		"""Get related blog posts"""
		filters = {
			"published": 1,
			"name": ["!=", self.name]
		}
		
		if self.blog_category:
			filters["blog_category"] = self.blog_category
			
		return frappe.get_all(
			"DMSOKO Blog Post",
			filters=filters,
			fields=["name", "title", "route", "blog_intro", "published_on", "meta_image"],
			order_by="published_on desc",
			limit=limit
		)
		
	@staticmethod
	def get_list_context(context):
		"""Set context for blog list page"""
		context.title = "Blog"
		context.introduction = "Read our latest blog posts"
		context.get_list = get_blog_list
		context.row_template = "templates/includes/blog_row.html"
		return context


def get_blog_list(doctype, txt=None, filters=None, limit_start=0, limit_page_length=20, order_by="published_on desc"):
	"""Get blog list for web view"""
	filters = filters or {}
	filters.update({"published": 1})
	
	if txt:
		filters.update({
			"title": ["like", f"%{txt}%"]
		})
		
	return frappe.get_all(
		"DMSOKO Blog Post",
		filters=filters,
		fields=["name", "title", "route", "blog_intro", "published_on", "meta_image", "author", "read_time"],
		order_by=order_by,
		start=limit_start,
		page_length=limit_page_length
	)


@frappe.whitelist(allow_guest=True)
def get_blog_posts(limit=6, page=1):
	"""Get published blog posts for API"""
	try:
		limit = int(limit) or 6
		page = int(page) or 1
		start = (page - 1) * limit
		
		posts = frappe.get_all(
			"DMSOKO Blog Post",
			filters={"published": 1},
			fields=[
				"name", "title", "blog_intro", "published_on", "route",
				"meta_image", "read_time", "author", "blog_category"
			],
			order_by="published_on desc, creation desc",
			start=start,
			page_length=limit
		)
		
		# Format the posts
		for post in posts:
			if post.published_on:
				post.published_date = frappe.utils.format_date(post.published_on, "medium")
			else:
				post.published_date = frappe.utils.format_date(post.creation, "medium")
			
			# Ensure we have a cover image
			if not post.meta_image:
				post.meta_image = "/assets/dmsoko/images/placeholder.svg"
			
			# Ensure we have intro text
			if not post.blog_intro:
				post.blog_intro = "Read this interesting blog post..."
			
			# Limit intro length
			if len(post.blog_intro) > 150:
				post.blog_intro = post.blog_intro[:150] + "..."
		
		# Get total count for pagination
		total = frappe.db.count("DMSOKO Blog Post", {"published": 1})
		
		return {
			"success": True,
			"data": posts,
			"total": total,
			"page": page,
			"limit": limit,
			"has_more": (start + limit) < total
		}
		
	except Exception as e:
		frappe.log_error(f"Error in get_blog_posts: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist(allow_guest=True)
def get_featured_blog_posts(limit=3):
	"""Get featured blog posts for API"""
	try:
		limit = int(limit) or 3
		
		posts = frappe.get_all(
			"DMSOKO Blog Post",
			filters={"published": 1, "featured": 1},
			fields=[
				"name", "title", "blog_intro", "published_on", "route",
				"meta_image", "read_time", "author"
			],
			order_by="published_on desc",
			limit=limit
		)

		# If no featured posts, get recent posts
		if not posts:
			posts = frappe.get_all(
				"DMSOKO Blog Post",
				filters={"published": 1},
				fields=[
					"name", "title", "blog_intro", "published_on", "route",
					"meta_image", "read_time", "author"
				],
				order_by="published_on desc",
				limit=limit
			)
		
		# Format the posts
		for post in posts:
			if post.published_on:
				post.published_date = frappe.utils.format_date(post.published_on, "medium")
			else:
				post.published_date = frappe.utils.format_date(post.creation, "medium")
			
			if not post.meta_image:
				post.meta_image = "/assets/dmsoko/images/placeholder.svg"
			
			if not post.blog_intro:
				post.blog_intro = "Read this interesting blog post..."
			
			if len(post.blog_intro) > 120:
				post.blog_intro = post.blog_intro[:120] + "..."
		
		return {"success": True, "data": posts}
		
	except Exception as e:
		frappe.log_error(f"Error in get_featured_blog_posts: {str(e)}")
		return {"success": False, "error": str(e)}

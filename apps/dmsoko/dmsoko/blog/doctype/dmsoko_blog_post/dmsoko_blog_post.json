{"actions": [], "allow_guest_to_view": 1, "allow_import": 1, "allow_rename": 1, "autoname": "field:title", "creation": "2025-06-23 12:00:00.000000", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["title", "blog_category", "author", "route", "read_time", "column_break_3", "published_on", "published", "featured", "disable_comments", "section_break_5", "blog_intro", "content_type", "content", "content_html", "meta_tags", "meta_title", "meta_description", "column_break_18", "meta_image"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "label": "Title", "reqd": 1, "unique": 1}, {"fieldname": "published_on", "fieldtype": "Date", "in_list_view": 1, "label": "Published On"}, {"default": "0", "fieldname": "published", "fieldtype": "Check", "in_list_view": 1, "label": "Published"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "blog_category", "fieldtype": "Data", "in_list_view": 1, "label": "Blog Category"}, {"fieldname": "author", "fieldtype": "Data", "in_list_view": 1, "label": "Author", "reqd": 1}, {"fieldname": "route", "fieldtype": "Data", "label": "Route", "unique": 1}, {"default": "5", "fieldname": "read_time", "fieldtype": "Int", "label": "Read Time (minutes)"}, {"default": "0", "fieldname": "featured", "fieldtype": "Check", "label": "Featured"}, {"default": "0", "fieldname": "disable_comments", "fieldtype": "Check", "label": "Disable Comments"}, {"fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Content"}, {"fieldname": "blog_intro", "fieldtype": "Small Text", "label": "Blog Intro"}, {"default": "Rich Text", "fieldname": "content_type", "fieldtype": "Select", "label": "Content Type", "options": "Rich Text\nMarkdown\nHTML"}, {"fieldname": "content", "fieldtype": "Text Editor", "label": "Content"}, {"fieldname": "content_html", "fieldtype": "HTML Editor", "label": "Content HTML", "read_only": 1}, {"fieldname": "meta_tags", "fieldtype": "Section Break", "label": "SEO Meta Tags"}, {"fieldname": "meta_title", "fieldtype": "Data", "label": "Meta Title"}, {"fieldname": "meta_description", "fieldtype": "Small Text", "label": "Meta Description"}, {"fieldname": "column_break_18", "fieldtype": "Column Break"}, {"fieldname": "meta_image", "fieldtype": "Attach Image", "label": "Meta Image"}], "has_web_view": 1, "index_web_pages_for_search": 1, "is_published_field": "published", "links": [], "modified": "2025-06-23 12:00:00.000000", "modified_by": "Administrator", "module": "Blog", "name": "DMSOKO Blog Post", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Blog Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Guest", "share": 1}], "route": "blog", "sort_field": "published_on", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1, "track_seen": 1, "track_views": 1}
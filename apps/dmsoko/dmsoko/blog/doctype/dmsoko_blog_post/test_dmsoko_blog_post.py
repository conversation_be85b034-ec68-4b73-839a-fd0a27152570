# Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.tests.utils import FrappeTestCase


class TestDMSOKOBlogPost(FrappeTestCase):
	def test_blog_post_creation(self):
		"""Test basic blog post creation"""
		blog_post = frappe.get_doc({
			"doctype": "DMSOKO Blog Post",
			"title": "Test Blog Post",
			"author": "Test Author",
			"content": "This is a test blog post content.",
			"published": 1
		})
		blog_post.insert()
		
		self.assertEqual(blog_post.title, "Test Blog Post")
		self.assertEqual(blog_post.author, "Test Author")
		self.assertTrue(blog_post.route)
		
		# Clean up
		blog_post.delete()
		
	def test_route_generation(self):
		"""Test automatic route generation from title"""
		blog_post = frappe.get_doc({
			"doctype": "DMSOKO Blog Post",
			"title": "Test Blog Post with Special Characters!@#",
			"author": "Test Author",
			"content": "This is a test blog post content.",
			"published": 1
		})
		blog_post.insert()
		
		self.assertEqual(blog_post.route, "test-blog-post-with-special-characters")
		
		# Clean up
		blog_post.delete()
		
	def test_intro_generation(self):
		"""Test automatic intro generation from content"""
		long_content = "This is a very long content. " * 20
		
		blog_post = frappe.get_doc({
			"doctype": "DMSOKO Blog Post",
			"title": "Test Blog Post",
			"author": "Test Author",
			"content": long_content,
			"published": 1
		})
		blog_post.insert()
		
		self.assertTrue(blog_post.blog_intro)
		self.assertTrue(len(blog_post.blog_intro) <= 203)  # 200 + "..."
		
		# Clean up
		blog_post.delete()
		
	def test_read_time_calculation(self):
		"""Test automatic read time calculation"""
		# Create content with approximately 400 words (should be 2 minutes)
		content = "word " * 400
		
		blog_post = frappe.get_doc({
			"doctype": "DMSOKO Blog Post",
			"title": "Test Blog Post",
			"author": "Test Author",
			"content": content,
			"published": 1
		})
		blog_post.insert()
		
		self.assertEqual(blog_post.read_time, 2)
		
		# Clean up
		blog_post.delete()

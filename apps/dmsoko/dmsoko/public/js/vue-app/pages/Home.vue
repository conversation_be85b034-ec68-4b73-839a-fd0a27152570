<template>
  <div class="home-page">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-orange-500 to-orange-700 text-white py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Find Everything You Need
        </h1>
        <p class="text-xl md:text-2xl mb-8 text-orange-100">
          Buy and sell anything in your local area with DMSoko
        </p>

        <!-- Search Bar -->
        <div class="max-w-2xl mx-auto">
          <div class="flex flex-col md:flex-row gap-4">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="What are you looking for?"
              class="flex-1 px-6 py-4 text-gray-900 rounded-lg text-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              @keyup.enter="performSearch"
            />
            <button
              @click="performSearch"
              class="bg-white text-orange-600 hover:bg-gray-100 font-semibold px-8 py-4 rounded-lg text-lg transition-colors"
            >
              Search
            </button>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="text-3xl font-bold">{{ stats.totalListings }}+</div>
            <div class="text-orange-200">Active Listings</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold">{{ stats.totalUsers }}+</div>
            <div class="text-orange-200">Happy Users</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold">{{ stats.totalCategories }}+</div>
            <div class="text-orange-200">Categories</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Categories Section -->
    <section class="py-12 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-12">Browse Categories</h2>
        
        <div v-if="categoriesLoading" class="text-center">
          <div class="spinner"></div>
          <p class="mt-2 text-gray-600">Loading categories...</p>
        </div>
        
        <div v-else class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
          <div
            v-for="category in popularCategories"
            :key="category.name"
            @click="browseCategory(category.name)"
            class="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow cursor-pointer"
          >
            <div class="text-3xl mb-3">
              <i :class="category.icon || 'fas fa-folder'" class="text-blue-600"></i>
            </div>
            <h3 class="font-semibold text-gray-900 mb-1">{{ category.category_name }}</h3>
            <p class="text-sm text-gray-500">{{ category.listing_count }} ads</p>
          </div>
        </div>
        
        <div class="text-center mt-8">
          <router-link
            to="/categories"
            class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition-colors"
          >
            View All Categories
          </router-link>
        </div>
      </div>
    </section>

    <!-- Featured Listings -->
    <section class="py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-8">Featured Listings</h2>

        <div v-if="featuredLoading" class="text-center">
          <div class="spinner"></div>
          <p class="mt-2 text-gray-600">Loading featured listings...</p>
        </div>

        <div v-else>
          <ListingCarousel :listings="featuredListings" />
        </div>

        <div class="text-center mt-8">
          <router-link
            to="/listings"
            class="inline-block bg-gray-900 hover:bg-gray-800 text-white font-semibold px-6 py-3 rounded-lg transition-colors"
          >
            View All Listings
          </router-link>
        </div>
      </div>
    </section>

    <!-- Recent Listings -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-12">Recent Listings</h2>
        
        <div v-if="recentLoading" class="text-center">
          <div class="spinner"></div>
          <p class="mt-2 text-gray-600">Loading recent listings...</p>
        </div>
        
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ListingCard
            v-for="listing in recentListings"
            :key="listing.name"
            :listing="listing"
            :compact="true"
          />
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-blue-600 text-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">Ready to Start Selling?</h2>
        <p class="text-xl mb-8 text-blue-100">
          Post your first ad for free and reach thousands of potential buyers
        </p>
        <router-link
          to="/post"
          class="inline-block bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold px-8 py-4 rounded-lg text-lg transition-colors"
        >
          Post Your Ad
        </router-link>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import ListingCard from '../components/ListingCard.vue'
import ListingCarousel from '../components/ListingCarousel.vue'

const router = useRouter()

// Data
const searchQuery = ref('')
const popularCategories = ref([])
const featuredListings = ref([])
const recentListings = ref([])
const categoriesLoading = ref(true)
const featuredLoading = ref(true)
const recentLoading = ref(true)

// Stats for hero section
const stats = ref({
  totalListings: '1000',
  totalUsers: '500',
  totalCategories: '50'
})

// Methods
const performSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      path: '/listings',
      query: { search: searchQuery.value.trim() }
    })
  }
}

const browseCategory = (categoryId) => {
  router.push({
    path: '/listings',
    query: { category: categoryId }
  })
}

const loadPopularCategories = async () => {
  try {
    const response = await window.frappe.call({
      method: 'dmsoko.api.categories.get_popular_categories',
      args: { limit: 12 }
    })
    
    if (response.message.success) {
      popularCategories.value = response.message.data
    }
  } catch (error) {
    console.error('Failed to load categories:', error)
  } finally {
    categoriesLoading.value = false
  }
}

const loadFeaturedListings = async () => {
  try {
    const response = await window.frappe.call({
      method: 'dmsoko.api.listings.get_featured_listings',
      args: { limit: 6 }
    })
    
    if (response.message.success) {
      featuredListings.value = response.message.data
    }
  } catch (error) {
    console.error('Failed to load featured listings:', error)
  } finally {
    featuredLoading.value = false
  }
}

const loadRecentListings = async () => {
  try {
    const response = await window.frappe.call({
      method: 'dmsoko.api.listings.get_recent_listings',
      args: { limit: 8 }
    })
    
    if (response.message.success) {
      recentListings.value = response.message.data
    }
  } catch (error) {
    console.error('Failed to load recent listings:', error)
  } finally {
    recentLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadPopularCategories()
  loadFeaturedListings()
  loadRecentListings()
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
}
</style>

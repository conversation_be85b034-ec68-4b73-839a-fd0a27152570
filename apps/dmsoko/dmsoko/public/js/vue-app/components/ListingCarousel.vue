<template>
  <div class="listing-carousel">
    <!-- Empty State -->
    <div v-if="!listings || listings.length === 0" class="text-center py-12">
      <div class="text-gray-400 mb-4">
        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
        </svg>
      </div>
      <p class="text-gray-500">No featured listings available</p>
    </div>

    <!-- Carousel -->
    <div v-else class="relative">
      <!-- Carousel Container -->
      <div
        ref="carouselContainer"
        class="flex overflow-x-auto scrollbar-hide snap-x snap-mandatory gap-6 pb-4"
        @scroll="updateScrollButtons"
      >
        <div
          v-for="listing in listings"
          :key="listing.name"
          class="flex-none w-72 md:w-80 snap-start"
        >
          <ListingCard :listing="listing" />
        </div>
      </div>

      <!-- Navigation Buttons -->
      <button
        v-if="showLeftButton"
        @click="scrollLeft"
        class="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-white hover:bg-gray-50 border border-gray-200 rounded-full p-3 shadow-lg transition-all duration-200 z-10"
        aria-label="Scroll left"
      >
        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>

      <button
        v-if="showRightButton"
        @click="scrollRight"
        class="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-white hover:bg-gray-50 border border-gray-200 rounded-full p-3 shadow-lg transition-all duration-200 z-10"
        aria-label="Scroll right"
      >
        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
      </div>

      <!-- Scroll Indicators -->
      <div v-if="scrollIndicators.length > 1" class="flex justify-center mt-4 space-x-2">
        <div
          v-for="(indicator, index) in scrollIndicators"
          :key="index"
          class="w-2 h-2 rounded-full transition-colors duration-200"
          :class="indicator.active ? 'bg-orange-600' : 'bg-gray-300'"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import ListingCard from './ListingCard.vue'

const props = defineProps({
  listings: {
    type: Array,
    required: true
  }
})

// Refs
const carouselContainer = ref(null)
const showLeftButton = ref(false)
const showRightButton = ref(false)
const currentScrollPosition = ref(0)

// Computed
const scrollIndicators = computed(() => {
  if (!props.listings.length) return []
  
  const itemsPerView = Math.floor(carouselContainer.value?.clientWidth / 320) || 3
  const totalIndicators = Math.ceil(props.listings.length / itemsPerView)
  const activeIndicator = Math.floor(currentScrollPosition.value / (carouselContainer.value?.scrollWidth / totalIndicators || 1))
  
  return Array.from({ length: totalIndicators }, (_, index) => ({
    active: index === activeIndicator
  }))
})

// Methods
const updateScrollButtons = () => {
  if (!carouselContainer.value) return
  
  const container = carouselContainer.value
  const scrollLeft = container.scrollLeft
  const maxScroll = container.scrollWidth - container.clientWidth
  
  showLeftButton.value = scrollLeft > 10
  showRightButton.value = scrollLeft < maxScroll - 10
  currentScrollPosition.value = scrollLeft
}

const scrollLeft = () => {
  if (!carouselContainer.value) return
  
  const container = carouselContainer.value
  const scrollAmount = container.clientWidth * 0.8
  container.scrollBy({
    left: -scrollAmount,
    behavior: 'smooth'
  })
}

const scrollRight = () => {
  if (!carouselContainer.value) return
  
  const container = carouselContainer.value
  const scrollAmount = container.clientWidth * 0.8
  container.scrollBy({
    left: scrollAmount,
    behavior: 'smooth'
  })
}

// Touch/swipe support
let startX = 0
let scrollLeftStart = 0

const handleTouchStart = (e) => {
  startX = e.touches[0].clientX
  scrollLeftStart = carouselContainer.value.scrollLeft
}

const handleTouchMove = (e) => {
  if (!startX) return
  
  const currentX = e.touches[0].clientX
  const diff = startX - currentX
  carouselContainer.value.scrollLeft = scrollLeftStart + diff
}

const handleTouchEnd = () => {
  startX = 0
  scrollLeftStart = 0
}

// Lifecycle
onMounted(async () => {
  await nextTick()
  updateScrollButtons()
  
  if (carouselContainer.value) {
    carouselContainer.value.addEventListener('touchstart', handleTouchStart, { passive: true })
    carouselContainer.value.addEventListener('touchmove', handleTouchMove, { passive: true })
    carouselContainer.value.addEventListener('touchend', handleTouchEnd, { passive: true })
    
    // Update buttons on resize
    window.addEventListener('resize', updateScrollButtons)
  }
})

onUnmounted(() => {
  if (carouselContainer.value) {
    carouselContainer.value.removeEventListener('touchstart', handleTouchStart)
    carouselContainer.value.removeEventListener('touchmove', handleTouchMove)
    carouselContainer.value.removeEventListener('touchend', handleTouchEnd)
  }
  window.removeEventListener('resize', updateScrollButtons)
})
</script>

<style scoped>
/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Smooth scrolling */
.listing-carousel {
  position: relative;
}

/* Ensure proper spacing for navigation buttons */
.listing-carousel .absolute {
  z-index: 10;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .listing-carousel .absolute {
    display: none; /* Hide navigation buttons on mobile, use touch instead */
  }
}
</style>

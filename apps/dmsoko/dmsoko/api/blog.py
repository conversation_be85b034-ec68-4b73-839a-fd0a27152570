# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import cint, get_datetime, today
import json


@frappe.whitelist(allow_guest=True)
def get_blog_posts(limit=6, page=1):
    """Get published blog posts"""
    try:
        limit = cint(limit) or 6
        page = cint(page) or 1
        start = (page - 1) * limit
        
        # Get published blog posts
        posts = frappe.db.sql("""
            SELECT 
                bp.name,
                bp.title,
                bp.blog_intro as intro,
                bp.published_on,
                bp.route,
                bp.meta_image as cover_image,
                bp.read_time,
                b.full_name as author_name,
                b.avatar as author_avatar,
                bc.title as category_title
            FROM `tabBlog Post` bp
            LEFT JOIN `tabBlogger` b ON bp.blogger = b.name
            LEFT JOIN `tabBlog Category` bc ON bp.blog_category = bc.name
            WHERE bp.published = 1
            ORDER BY bp.published_on DESC, bp.creation DESC
            LIMIT %s OFFSET %s
        """, (limit, start), as_dict=True)
        
        # Format the posts
        for post in posts:
            if post.published_on:
                post.published_date = frappe.utils.format_date(post.published_on, "medium")
            else:
                post.published_date = frappe.utils.format_date(post.creation, "medium")
            
            # Ensure we have a cover image
            if not post.cover_image:
                post.cover_image = "/assets/dmsoko/images/placeholder.svg"
            
            # Ensure we have intro text
            if not post.intro:
                post.intro = "Read this interesting blog post..."
            
            # Limit intro length
            if len(post.intro) > 150:
                post.intro = post.intro[:150] + "..."
        
        # Get total count for pagination
        total = frappe.db.count("Blog Post", {"published": 1})
        
        return {
            "success": True,
            "data": posts,
            "total": total,
            "page": page,
            "limit": limit,
            "has_more": (start + limit) < total
        }
        
    except Exception as e:
        frappe.log_error(f"Error in get_blog_posts: {str(e)}")
        return {"success": False, "error": str(e)}


@frappe.whitelist(allow_guest=True)
def get_blog_post(route):
    """Get single blog post by route"""
    try:
        post = frappe.db.sql("""
            SELECT 
                bp.name,
                bp.title,
                bp.content,
                bp.content_type,
                bp.content_html,
                bp.blog_intro as intro,
                bp.published_on,
                bp.route,
                bp.meta_image as cover_image,
                bp.meta_title,
                bp.meta_description,
                bp.read_time,
                b.full_name as author_name,
                b.avatar as author_avatar,
                b.bio as author_bio,
                bc.title as category_title,
                bc.route as category_route
            FROM `tabBlog Post` bp
            LEFT JOIN `tabBlogger` b ON bp.blogger = b.name
            LEFT JOIN `tabBlog Category` bc ON bp.blog_category = bc.name
            WHERE bp.route = %s AND bp.published = 1
        """, (route,), as_dict=True)
        
        if not post:
            return {"success": False, "error": "Blog post not found"}
        
        post = post[0]
        
        # Format published date
        if post.published_on:
            post.published_date = frappe.utils.format_date(post.published_on, "medium")
        else:
            post.published_date = frappe.utils.format_date(post.creation, "medium")
        
        # Get content based on content type
        if post.content_type == "HTML" and post.content_html:
            post.content = post.content_html
        elif not post.content:
            post.content = post.intro or ""
        
        return {"success": True, "data": post}
        
    except Exception as e:
        frappe.log_error(f"Error in get_blog_post: {str(e)}")
        return {"success": False, "error": str(e)}


@frappe.whitelist(allow_guest=True)
def get_featured_blog_posts(limit=3):
    """Get featured blog posts"""
    try:
        limit = cint(limit) or 3
        
        # Get featured blog posts
        posts = frappe.db.sql("""
            SELECT 
                bp.name,
                bp.title,
                bp.blog_intro as intro,
                bp.published_on,
                bp.route,
                bp.meta_image as cover_image,
                bp.read_time,
                b.full_name as author_name
            FROM `tabBlog Post` bp
            LEFT JOIN `tabBlogger` b ON bp.blogger = b.name
            WHERE bp.published = 1 AND bp.featured = 1
            ORDER BY bp.published_on DESC
            LIMIT %s
        """, (limit,), as_dict=True)
        
        # If no featured posts, get recent posts
        if not posts:
            posts = frappe.db.sql("""
                SELECT 
                    bp.name,
                    bp.title,
                    bp.blog_intro as intro,
                    bp.published_on,
                    bp.route,
                    bp.meta_image as cover_image,
                    bp.read_time,
                    b.full_name as author_name
                FROM `tabBlog Post` bp
                LEFT JOIN `tabBlogger` b ON bp.blogger = b.name
                WHERE bp.published = 1
                ORDER BY bp.published_on DESC
                LIMIT %s
            """, (limit,), as_dict=True)
        
        # Format the posts
        for post in posts:
            if post.published_on:
                post.published_date = frappe.utils.format_date(post.published_on, "medium")
            else:
                post.published_date = frappe.utils.format_date(post.creation, "medium")
            
            if not post.cover_image:
                post.cover_image = "/assets/dmsoko/images/placeholder.svg"
            
            if not post.intro:
                post.intro = "Read this interesting blog post..."
            
            if len(post.intro) > 120:
                post.intro = post.intro[:120] + "..."
        
        return {"success": True, "data": posts}
        
    except Exception as e:
        frappe.log_error(f"Error in get_featured_blog_posts: {str(e)}")
        return {"success": False, "error": str(e)}

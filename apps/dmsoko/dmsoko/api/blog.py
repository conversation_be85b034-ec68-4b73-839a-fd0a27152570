# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import cint, get_datetime, today
import json


@frappe.whitelist(allow_guest=True)
def get_blog_posts(limit=6, page=1):
    """Get published blog posts"""
    try:
        limit = cint(limit) or 6
        page = cint(page) or 1
        start = (page - 1) * limit

        # Get published blog posts from custom Blog Post DocType
        posts = frappe.get_all(
            "Blog Post",
            filters={"published": 1},
            fields=[
                "name", "title", "blog_intro", "published_on", "route",
                "meta_image", "read_time", "author", "blog_category"
            ],
            order_by="published_on desc, creation desc",
            start=start,
            page_length=limit
        )

        # Format the posts
        for post in posts:
            if post.published_on:
                post.published_date = frappe.utils.format_date(post.published_on, "medium")
            else:
                post.published_date = frappe.utils.format_date(post.creation, "medium")

            # Ensure we have a cover image
            if not post.meta_image:
                post.meta_image = "/assets/dmsoko/images/placeholder.svg"

            # Ensure we have intro text
            if not post.blog_intro:
                post.blog_intro = "Read this interesting blog post..."

            # Limit intro length
            if len(post.blog_intro) > 150:
                post.blog_intro = post.blog_intro[:150] + "..."

            # Rename fields for frontend compatibility
            post.intro = post.blog_intro
            post.cover_image = post.meta_image
            post.author_name = post.author

        # Get total count for pagination
        total = frappe.db.count("Blog Post", {"published": 1})

        return {
            "success": True,
            "data": posts,
            "total": total,
            "page": page,
            "limit": limit,
            "has_more": (start + limit) < total
        }
        
    except Exception as e:
        frappe.log_error(f"Error in get_blog_posts: {str(e)}")
        return {"success": False, "error": str(e)}


@frappe.whitelist(allow_guest=True)
def get_blog_post(route):
    """Get single blog post by route"""
    try:
        post = frappe.get_all(
            "Blog Post",
            filters={"route": route, "published": 1},
            fields=[
                "name", "title", "content", "content_type", "content_html",
                "blog_intro", "published_on", "route", "meta_image",
                "meta_title", "meta_description", "read_time", "author",
                "blog_category"
            ],
            limit=1
        )
        
        if not post:
            return {"success": False, "error": "Blog post not found"}
        
        post = post[0]
        
        # Format published date
        if post.published_on:
            post.published_date = frappe.utils.format_date(post.published_on, "medium")
        else:
            post.published_date = frappe.utils.format_date(post.creation, "medium")
        
        # Get content based on content type
        if post.content_type == "HTML" and post.content_html:
            post.content = post.content_html
        elif not post.content:
            post.content = post.blog_intro or ""

        # Rename fields for frontend compatibility
        post.intro = post.blog_intro
        post.cover_image = post.meta_image or "/assets/dmsoko/images/placeholder.svg"
        post.author_name = post.author
        post.category_title = post.blog_category

        return {"success": True, "data": post}
        
    except Exception as e:
        frappe.log_error(f"Error in get_blog_post: {str(e)}")
        return {"success": False, "error": str(e)}


@frappe.whitelist(allow_guest=True)
def get_featured_blog_posts(limit=3):
    """Get featured blog posts"""
    try:
        limit = cint(limit) or 3
        
        # Get featured blog posts from custom Blog Post DocType
        posts = frappe.get_all(
            "Blog Post",
            filters={"published": 1, "featured": 1},
            fields=[
                "name", "title", "blog_intro", "published_on", "route",
                "meta_image", "read_time", "author"
            ],
            order_by="published_on desc",
            limit=limit
        )

        # If no featured posts, get recent posts
        if not posts:
            posts = frappe.get_all(
                "Blog Post",
                filters={"published": 1},
                fields=[
                    "name", "title", "blog_intro", "published_on", "route",
                    "meta_image", "read_time", "author"
                ],
                order_by="published_on desc",
                limit=limit
            )
        
        # Format the posts
        for post in posts:
            if post.published_on:
                post.published_date = frappe.utils.format_date(post.published_on, "medium")
            else:
                post.published_date = frappe.utils.format_date(post.creation, "medium")

            if not post.meta_image:
                post.meta_image = "/assets/dmsoko/images/placeholder.svg"

            if not post.blog_intro:
                post.blog_intro = "Read this interesting blog post..."

            if len(post.blog_intro) > 120:
                post.blog_intro = post.blog_intro[:120] + "..."

            # Rename fields for frontend compatibility
            post.intro = post.blog_intro
            post.cover_image = post.meta_image
            post.author_name = post.author
        
        return {"success": True, "data": posts}
        
    except Exception as e:
        frappe.log_error(f"Error in get_featured_blog_posts: {str(e)}")
        return {"success": False, "error": str(e)}

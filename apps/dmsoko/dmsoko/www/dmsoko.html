<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title or "DMSoko - Your Marketplace" }}</title>
    <meta name="description" content="DMSoko - Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta name="keywords" content="classified ads, marketplace, buy, sell, local, cars, jobs, real estate">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ frappe.utils.get_url() }}/dmsoko">
    <meta property="og:title" content="{{ title or 'DMSoko - Your Marketplace' }}">
    <meta property="og:description" content="Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta property="og:image" content="{{ frappe.utils.get_url() }}/assets/dmsoko/images/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ frappe.utils.get_url() }}/dmsoko">
    <meta property="twitter:title" content="{{ title or 'DMSoko - Your Marketplace' }}">
    <meta property="twitter:description" content="Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta property="twitter:image" content="{{ frappe.utils.get_url() }}/assets/dmsoko/images/og-image.png">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/dmsoko/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/dmsoko/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/dmsoko/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/dmsoko/images/favicon-16x16.png">

    <!-- CSS -->
    <link rel="stylesheet" href="/assets/frappe/css/frappe-web.css">
    <link rel="stylesheet" href="/assets/dmsoko/css/dmsoko-ui.css">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- DMSoko Custom Styles -->
    <style>
        :root {
            --dmsoko-blue: #4A90E2;
            --dmsoko-green: #7ED321;
            --dmsoko-orange: #F5A623;
            --dmsoko-red: #D0021B;
            --dmsoko-dark: #2C3E50;
            --dmsoko-light: #ECF0F1;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .animate-spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .scroll-container {
            scrollbar-width: thin;
            scrollbar-color: var(--dmsoko-blue) transparent;
        }

        .scroll-container::-webkit-scrollbar {
            height: 6px;
        }

        .scroll-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .scroll-container::-webkit-scrollbar-thumb {
            background-color: var(--dmsoko-blue);
            border-radius: 3px;
        }

        .featured-badge {
            background: linear-gradient(45deg, var(--dmsoko-orange), #FFB84D);
            color: white;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .hero-gradient {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
    </style>

    <!-- Frappe Framework -->
    <script>
        window.frappe = window.frappe || {};
        window.frappe.ready_events = [];
        window.frappe.ready = function(fn) {
            window.frappe.ready_events.push(fn);
        };
        
        // Frappe session data
        window.frappe.session = {
            user: "{{ frappe.session.user }}",
            user_fullname: "{{ frappe.session.data.full_name or '' }}",
            user_image: "{{ frappe.session.data.user_image or '' }}",
            csrf_token: "{{ frappe.session.csrf_token }}"
        };
        
        // Frappe call function
        window.frappe.call = function(opts) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/api/method/' + opts.method);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('X-Frappe-CSRF-Token', window.frappe.session.csrf_token);
                
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            reject(new Error('Invalid JSON response'));
                        }
                    } else {
                        reject(new Error('HTTP ' + xhr.status + ': ' + xhr.statusText));
                    }
                };
                
                xhr.onerror = function() {
                    reject(new Error('Network error'));
                };
                
                xhr.send(JSON.stringify(opts.args || {}));
            });
        };
    </script>
</head>
<body class="bg-gray-50">
    <!-- Vue App Container -->
    <div id="dmsoko-app">
        <!-- Navigation Bar -->
        <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo -->
                    <div class="flex items-center">
                        <a href="/dmsoko" class="flex items-center">
                            <img src="/assets/dmsoko/images/dmsoko-logo.svg" alt="DMSOKO" class="h-10 w-auto">
                        </a>
                    </div>

                    <!-- Navigation Links -->
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="/dmsoko" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                            Home
                        </a>
                        <a href="/blog" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                            Blog
                        </a>
                        <a href="/contact" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                            Contact Us
                        </a>
                        <a href="/signup" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Sign Up
                        </a>
                    </div>

                    <!-- Mobile menu button -->
                    <div class="md:hidden">
                        <button type="button" id="mobile-menu-button" class="text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Mobile menu -->
                <div id="mobile-menu" class="md:hidden hidden">
                    <div class="px-2 pt-2 pb-3 space-y-1 border-t border-gray-200">
                        <a href="/dmsoko" class="block text-gray-700 hover:text-blue-600 px-3 py-2 text-base font-medium">Home</a>
                        <a href="/blog" class="block text-gray-700 hover:text-blue-600 px-3 py-2 text-base font-medium">Blog</a>
                        <a href="/contact" class="block text-gray-700 hover:text-blue-600 px-3 py-2 text-base font-medium">Contact Us</a>
                        <a href="/signup" class="block bg-blue-500 text-white px-3 py-2 rounded-lg hover:bg-blue-600 transition-colors text-base font-medium">Sign Up</a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Loading state -->
        <div id="loading-state" class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Loading DMSoko...</p>
            </div>
        </div>

        <!-- Main Content -->
        <div id="main-content" class="min-h-screen bg-gray-50" style="display: none;">
            <!-- Hero Section -->
            <section class="hero-gradient py-16 px-4">
                <div class="max-w-6xl mx-auto text-center">
                    <!-- Logo and Title -->
                    <div class="mb-8">
                        <div class="flex items-center justify-center mb-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg mr-3 flex items-center justify-center">
                                <span class="text-white font-bold text-xl">DM</span>
                            </div>
                            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg transform rotate-12"></div>
                        </div>
                        <h1 class="text-5xl font-bold mb-4">
                            <span class="hero-title">Find Anything Around You.</span>
                        </h1>
                    </div>

                    <!-- Search Section -->
                    <div class="max-w-4xl mx-auto mb-8">
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <div class="flex flex-col md:flex-row gap-4">
                                <div class="flex-1">
                                    <input
                                        id="search-keyword"
                                        type="text"
                                        placeholder="Enter Keywords"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>
                                <div class="flex-1">
                                    <select
                                        id="search-location"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">Select Location</option>
                                        <option value="dar-es-salaam">Dar es Salaam</option>
                                        <option value="arusha">Arusha</option>
                                        <option value="mwanza">Mwanza</option>
                                        <option value="dodoma">Dodoma</option>
                                    </select>
                                </div>
                                <button
                                    type="button"
                                    onclick="handleSearch()"
                                    class="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
                                >
                                    🔍 Search
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Popular Search -->
                    <div class="text-center">
                        <p class="text-gray-700 mb-4 font-medium">Popular Search:</p>
                        <div id="popular-categories" class="flex flex-wrap justify-center gap-3">
                            <!-- Popular categories will be loaded here -->
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Real Estate</span>
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Mobile Phones</span>
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Electronics</span>
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Vehicles</span>
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Pets</span>
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Animal</span>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- Featured Items Scroll -->
        <section class="py-12 px-4">
            <div class="max-w-6xl mx-auto">
                <div class="flex justify-between items-center mb-8">
                    <h2 class="text-2xl font-bold text-gray-900">Featured Items</h2>
                    <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">View All</a>
                </div>

                <div class="overflow-x-auto scroll-container">
                    <div id="featured-items-container" class="flex gap-6 pb-4 w-max-content">
                        <!-- Featured items will be loaded here -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden w-80 flex-shrink-0">
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-900 mb-2">Loading...</h3>
                                <p class="text-sm text-gray-600">Items will appear here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Ads Section -->
        <section class="py-12 px-4 bg-white">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Featured Ads</h2>
                    <p class="text-gray-600">Check out our special items</p>
                </div>

                <div id="featured-ads-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Featured ads will be loaded here -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border">
                        <div class="relative">
                            <img
                                src="/assets/dmsoko/images/placeholder.svg"
                                alt="Baby Walker"
                                class="w-full h-40 object-cover"
                            />
                            <div class="absolute top-2 left-2">
                                <span class="featured-badge px-2 py-1 rounded text-xs font-bold">
                                    FEATURED
                                </span>
                            </div>
                            <div class="absolute bottom-2 right-2">
                                <button class="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md text-xs">
                                    ❤️
                                </button>
                            </div>
                        </div>
                        <div class="p-3">
                            <h3 class="font-semibold text-gray-900 mb-1 text-sm">Baby Walker</h3>
                            <p class="text-xs text-gray-600 mb-2">📍 Ubungo, DAR ES SALAAM</p>
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-bold text-dmsoko-blue">
                                    TZS 110,000
                                </span>
                                <button class="text-xs text-gray-500 hover:text-gray-700">
                                    View →
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border">
                        <div class="relative">
                            <img
                                src="/assets/dmsoko/images/placeholder.svg"
                                alt="Samsung A05"
                                class="w-full h-40 object-cover"
                            />
                            <div class="absolute top-2 left-2">
                                <span class="featured-badge px-2 py-1 rounded text-xs font-bold">
                                    FEATURED
                                </span>
                            </div>
                            <div class="absolute bottom-2 right-2">
                                <button class="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md text-xs">
                                    ❤️
                                </button>
                            </div>
                        </div>
                        <div class="p-3">
                            <h3 class="font-semibold text-gray-900 mb-1 text-sm">Samsung A05 Mpya</h3>
                            <p class="text-xs text-gray-600 mb-2">📍 DAR ES SALAAM</p>
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-bold text-dmsoko-blue">
                                    TZS 250,000
                                </span>
                                <button class="text-xs text-gray-500 hover:text-gray-700">
                                    View →
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border">
                        <div class="relative">
                            <img
                                src="/assets/dmsoko/images/placeholder.svg"
                                alt="Baby Bathtub Set"
                                class="w-full h-40 object-cover"
                            />
                            <div class="absolute top-2 left-2">
                                <span class="featured-badge px-2 py-1 rounded text-xs font-bold">
                                    FEATURED
                                </span>
                            </div>
                            <div class="absolute bottom-2 right-2">
                                <button class="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md text-xs">
                                    ❤️
                                </button>
                            </div>
                        </div>
                        <div class="p-3">
                            <h3 class="font-semibold text-gray-900 mb-1 text-sm">Baby Bathtub Set</h3>
                            <p class="text-xs text-gray-600 mb-2">📍 Ubungo, DAR ES SALAAM</p>
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-bold text-dmsoko-blue">
                                    TZS 110,000
                                </span>
                                <button class="text-xs text-gray-500 hover:text-gray-700">
                                    View →
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border">
                        <div class="relative">
                            <img
                                src="/assets/dmsoko/images/placeholder.svg"
                                alt="BEDSHEETS"
                                class="w-full h-40 object-cover"
                            />
                            <div class="absolute top-2 left-2">
                                <span class="featured-badge px-2 py-1 rounded text-xs font-bold">
                                    FEATURED
                                </span>
                            </div>
                            <div class="absolute bottom-2 right-2">
                                <button class="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md text-xs">
                                    ❤️
                                </button>
                            </div>
                        </div>
                        <div class="p-3">
                            <h3 class="font-semibold text-gray-900 mb-1 text-sm">BEDSHEETS</h3>
                            <p class="text-xs text-gray-600 mb-2">📍 Ubungo, DAR ES SALAAM</p>
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-bold text-dmsoko-blue">
                                    TZS 55,000
                                </span>
                                <button class="text-xs text-gray-500 hover:text-gray-700">
                                    View →
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- All Items Section -->
        <section id="all-items-section" class="py-12 px-4">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">All Items</h2>
                    <p class="text-gray-600">Browse all available items</p>
                </div>

                <div id="all-items-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <!-- All items will be loaded here -->
                </div>

                <div class="text-center mt-8">
                    <button class="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
                        Load More Items
                    </button>
                </div>
            </div>
        </section>

        <!-- Blog Section -->
        <section class="py-12 px-4 bg-white">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Latest Blog & Articles</h2>
                    <p class="text-gray-600">Stay updated with our latest news and tips</p>
                </div>

                <div id="blog-posts-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <article class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                        <img
                            src="/assets/dmsoko/images/placeholder.svg"
                            alt="Blog post"
                            class="w-full h-48 object-cover"
                        />
                        <div class="p-6">
                            <div class="text-sm text-gray-500 mb-2">June 23, 2025</div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                Tips for Selling Your Items Quickly
                            </h3>
                            <p class="text-gray-600 mb-4 line-clamp-3">
                                Learn the best practices for creating compelling listings that attract buyers and sell fast.
                            </p>
                            <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">
                                Read More →
                            </a>
                        </div>
                    </article>

                    <article class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                        <img
                            src="/assets/dmsoko/images/placeholder.svg"
                            alt="Blog post"
                            class="w-full h-48 object-cover"
                        />
                        <div class="p-6">
                            <div class="text-sm text-gray-500 mb-2">June 20, 2025</div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                Safety Guidelines for Online Trading
                            </h3>
                            <p class="text-gray-600 mb-4 line-clamp-3">
                                Essential safety tips to protect yourself when buying and selling online.
                            </p>
                            <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">
                                Read More →
                            </a>
                        </div>
                    </article>

                    <article class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                        <img
                            src="/assets/dmsoko/images/placeholder.svg"
                            alt="Blog post"
                            class="w-full h-48 object-cover"
                        />
                        <div class="p-6">
                            <div class="text-sm text-gray-500 mb-2">June 18, 2025</div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                How to Take Great Product Photos
                            </h3>
                            <p class="text-gray-600 mb-4 line-clamp-3">
                                Photography tips to make your listings stand out and attract more buyers.
                            </p>
                            <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">
                                Read More →
                            </a>
                        </div>
                    </article>
                </div>
            </div>
        </section>
    </div>

    <!-- Fallback content for users without JavaScript -->
    <noscript>
        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="max-w-md mx-auto text-center">
                <h1 class="text-2xl font-bold text-gray-900 mb-4">JavaScript Required</h1>
                <p class="text-gray-600 mb-4">
                    DMSoko requires JavaScript to function properly. Please enable JavaScript in your browser and refresh the page.
                </p>
                <a href="/dmsoko" class="inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                    Refresh Page
                </a>
            </div>
        </div>
    </noscript>

    <!-- DMSoko App Script -->
    <script>
        // Global data storage
        let appData = {
            featuredListings: [],
            allListings: [],
            blogPosts: [],
            popularCategories: [],
            searchResults: [],
            isSearching: false,
            currentSearchQuery: '',
            currentSearchLocation: ''
        };

        // API calling functions
        async function callAPI(method, args = {}) {
            try {
                const response = await fetch('/api/method/' + method, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Frappe-CSRF-Token': window.frappe?.session?.csrf_token || ''
                    },
                    body: JSON.stringify(args)
                });

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('API call failed:', error);
                return { success: false, error: error.message };
            }
        }

        // Function to format price
        function formatPrice(price, currency = 'TZS') {
            if (!price) return 'Price on request';
            return new Intl.NumberFormat('en-TZ', {
                style: 'currency',
                currency: currency,
                minimumFractionDigits: 0
            }).format(price);
        }

        // Function to handle search
        async function handleSearch() {
            const keyword = document.getElementById('search-keyword').value.trim();
            const location = document.getElementById('search-location').value;

            if (!keyword && !location) {
                // If no search terms, show all listings
                await loadAllListings();
                return;
            }

            console.log('Searching for:', keyword, 'in', location);
            appData.isSearching = true;
            appData.currentSearchQuery = keyword;
            appData.currentSearchLocation = location;

            try {
                // Show loading state
                showLoadingState('Searching...');

                // Call search API
                const response = await callAPI('dmsoko.api.search.search_listings', {
                    query: keyword,
                    filters: JSON.stringify({
                        location: location || undefined
                    }),
                    page: 1,
                    page_size: 20
                });

                if (response.success) {
                    appData.searchResults = response.data || [];
                    renderSearchResults();
                } else {
                    console.error('Search failed:', response.error);
                    showError('Search failed. Please try again.');
                }
            } catch (error) {
                console.error('Search error:', error);
                showError('Search failed. Please try again.');
            } finally {
                hideLoadingState();
            }
        }

        // Function to load featured listings
        async function loadFeaturedListings() {
            try {
                const response = await callAPI('dmsoko.api.listings.get_featured_listings', {
                    limit: 6
                });

                if (response.success) {
                    appData.featuredListings = response.data || [];
                } else {
                    console.error('Failed to load featured listings:', response.error);
                }
            } catch (error) {
                console.error('Error loading featured listings:', error);
            }
        }

        // Function to load all listings
        async function loadAllListings() {
            try {
                const response = await callAPI('dmsoko.api.listings.get_listings', {
                    page: 1,
                    page_size: 12,
                    sort_by: 'creation',
                    sort_order: 'desc'
                });

                if (response.success) {
                    appData.allListings = response.data || [];
                    appData.isSearching = false;
                } else {
                    console.error('Failed to load listings:', response.error);
                }
            } catch (error) {
                console.error('Error loading listings:', error);
            }
        }

        // Function to load popular categories
        async function loadPopularCategories() {
            try {
                const response = await callAPI('dmsoko.api.categories.get_popular_categories', {
                    limit: 6
                });

                if (response.success) {
                    appData.popularCategories = response.data || [];
                } else {
                    console.error('Failed to load categories:', response.error);
                    // Fallback to default categories
                    appData.popularCategories = [
                        { category_name: 'Real Estate' },
                        { category_name: 'Mobile Phones' },
                        { category_name: 'Electronics' },
                        { category_name: 'Vehicles' },
                        { category_name: 'Pets' },
                        { category_name: 'Animal' }
                    ];
                }
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }

        // Function to load blog posts
        async function loadBlogPosts() {
            try {
                const response = await callAPI('dmsoko.api.blog.get_featured_blog_posts', {
                    limit: 3
                });

                if (response.success) {
                    appData.blogPosts = response.data || [];
                } else {
                    console.error('Failed to load blog posts:', response.error);
                }
            } catch (error) {
                console.error('Error loading blog posts:', error);
            }
        }

        // Utility functions
        function showLoadingState(message = 'Loading...') {
            const loadingDiv = document.getElementById('loading-state');
            if (loadingDiv) {
                loadingDiv.querySelector('p').textContent = message;
                loadingDiv.style.display = 'flex';
            }
        }

        function hideLoadingState() {
            const loadingDiv = document.getElementById('loading-state');
            const mainContent = document.getElementById('main-content');
            if (loadingDiv) loadingDiv.style.display = 'none';
            if (mainContent) mainContent.style.display = 'block';
        }

        function showError(message) {
            // You can implement a toast notification here
            alert(message);
        }

        // Function to render featured items
        function renderFeaturedItems() {
            const container = document.getElementById('featured-items-container');
            if (!container) return;

            const items = appData.featuredListings;
            if (!items || items.length === 0) {
                container.innerHTML = '<div class="text-center py-8 text-gray-500">No featured items available</div>';
                return;
            }

            container.innerHTML = items.map(listing => `
                <div class="bg-white rounded-lg shadow-md overflow-hidden w-80 flex-shrink-0 hover:shadow-lg transition-shadow cursor-pointer">
                    <div class="relative">
                        <img
                            src="${listing.primary_image || '/assets/dmsoko/images/placeholder.svg'}"
                            alt="${listing.title}"
                            class="w-full h-48 object-cover"
                            onerror="this.src='/assets/dmsoko/images/placeholder.svg'"
                        />
                        <div class="absolute top-3 left-3">
                            <span class="featured-badge px-3 py-1 rounded-full text-sm font-bold">
                                FEATURED
                            </span>
                        </div>
                        <div class="absolute bottom-3 right-3">
                            <button type="button" class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-gray-100" onclick="toggleWishlist('${listing.name}')">
                                ❤️
                            </button>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2">${listing.title}</h3>
                        <p class="text-sm text-gray-600 mb-3">📍 ${listing.location || 'Location not specified'}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xl font-bold text-dmsoko-blue">
                                ${formatPrice(listing.price, listing.currency)}
                            </span>
                            <span class="text-sm text-gray-500">${listing.category_name || listing.category || 'Uncategorized'}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Function to render popular categories
        function renderPopularCategories() {
            const container = document.getElementById('popular-categories');
            if (!container) return;

            const categories = appData.popularCategories;
            if (!categories || categories.length === 0) return;

            container.innerHTML = categories.map(category => `
                <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors" onclick="searchByCategory('${category.category_name || category.name}')">
                    ${category.category_name || category.name}
                </span>
            `).join('');
        }

        // Function to render featured ads
        function renderFeaturedAds() {
            const container = document.getElementById('featured-ads-container');
            if (!container) return;

            const items = appData.featuredListings.slice(0, 4); // Show first 4 featured items
            if (!items || items.length === 0) {
                container.innerHTML = '<div class="col-span-full text-center py-8 text-gray-500">No featured ads available</div>';
                return;
            }

            container.innerHTML = items.map(listing => `
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border">
                    <div class="relative">
                        <img
                            src="${listing.primary_image || '/assets/dmsoko/images/placeholder.svg'}"
                            alt="${listing.title}"
                            class="w-full h-40 object-cover"
                            onerror="this.src='/assets/dmsoko/images/placeholder.svg'"
                        />
                        <div class="absolute top-2 left-2">
                            <span class="featured-badge px-2 py-1 rounded text-xs font-bold">
                                FEATURED
                            </span>
                        </div>
                        <div class="absolute bottom-2 right-2">
                            <button type="button" class="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md text-xs" onclick="toggleWishlist('${listing.name}')">
                                ❤️
                            </button>
                        </div>
                    </div>
                    <div class="p-3">
                        <h3 class="font-semibold text-gray-900 mb-1 text-sm">${listing.title}</h3>
                        <p class="text-xs text-gray-600 mb-2">📍 ${listing.location || 'Location not specified'}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-bold text-dmsoko-blue">
                                ${formatPrice(listing.price, listing.currency)}
                            </span>
                            <button type="button" class="text-xs text-gray-500 hover:text-gray-700" onclick="viewListing('${listing.name}')">
                                View →
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Function to render all items
        function renderAllItems() {
            const container = document.getElementById('all-items-container');
            if (!container) return;

            const items = appData.isSearching ? appData.searchResults : appData.allListings;
            if (!items || items.length === 0) {
                const message = appData.isSearching ? 'No items found for your search.' : 'No items available';
                container.innerHTML = `<div class="col-span-full text-center py-8 text-gray-500">${message}</div>`;
                return;
            }

            container.innerHTML = items.map(listing => `
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                    <div class="relative">
                        <img
                            src="${listing.primary_image || '/assets/dmsoko/images/placeholder.svg'}"
                            alt="${listing.title}"
                            class="w-full h-48 object-cover"
                            onerror="this.src='/assets/dmsoko/images/placeholder.svg'"
                        />
                        <div class="absolute top-3 right-3">
                            <button type="button" class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-gray-100" onclick="toggleWishlist('${listing.name}')">
                                ❤️
                            </button>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2">${listing.title}</h3>
                        <p class="text-sm text-gray-600 mb-3">📍 ${listing.location || 'Location not specified'}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xl font-bold text-dmsoko-blue">
                                ${formatPrice(listing.price, listing.currency)}
                            </span>
                            <span class="text-sm text-gray-500">${listing.category_name || listing.category || 'Uncategorized'}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Function to render blog posts
        function renderBlogPosts() {
            const container = document.querySelector('#blog-posts-container');
            if (!container) return;

            const posts = appData.blogPosts;
            if (!posts || posts.length === 0) {
                // Keep static content if no dynamic posts
                return;
            }

            container.innerHTML = posts.map(post => `
                <article class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                    <img
                        src="${post.cover_image || '/assets/dmsoko/images/placeholder.svg'}"
                        alt="${post.title}"
                        class="w-full h-48 object-cover"
                        onerror="this.src='/assets/dmsoko/images/placeholder.svg'"
                    />
                    <div class="p-6">
                        <div class="text-sm text-gray-500 mb-2">${post.published_date || 'Recently'}</div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">
                            ${post.title}
                        </h3>
                        <p class="text-gray-600 mb-4 line-clamp-3">
                            ${post.intro || 'Read this interesting blog post...'}
                        </p>
                        <a href="/blog/${post.route || '#'}" class="text-blue-500 hover:text-blue-600 font-medium">
                            Read More →
                        </a>
                    </div>
                </article>
            `).join('');
        }

        // Function to render search results
        function renderSearchResults() {
            // Update the "All Items" section title to show search results
            const allItemsTitle = document.querySelector('#all-items-section h2');
            if (allItemsTitle) {
                allItemsTitle.textContent = `Search Results for "${appData.currentSearchQuery}"`;
            }

            renderAllItems();
        }

        // Utility functions for interactions
        function toggleWishlist(listingId) {
            console.log('Toggle wishlist for:', listingId);
            // Implement wishlist functionality
        }

        function viewListing(listingId) {
            console.log('View listing:', listingId);
            // Implement listing view functionality
            window.location.href = `/listing/${listingId}`;
        }

        function searchByCategory(categoryName) {
            document.getElementById('search-keyword').value = categoryName;
            handleSearch();
        }

        // Mobile menu toggle
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.toggle('hidden');
            }
        }

        // Initialize app when DOM is ready
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('Initializing DMSoko app...');

            // Setup mobile menu
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', toggleMobileMenu);
            }

            // Setup search on Enter key
            const searchInput = document.getElementById('search-keyword');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        handleSearch();
                    }
                });
            }

            try {
                // Load all data
                showLoadingState('Loading content...');

                await Promise.all([
                    loadPopularCategories(),
                    loadFeaturedListings(),
                    loadAllListings(),
                    loadBlogPosts()
                ]);

                // Render all sections
                renderPopularCategories();
                renderFeaturedItems();
                renderFeaturedAds();
                renderAllItems();
                renderBlogPosts();

                console.log('DMSoko app initialized successfully!');
            } catch (error) {
                console.error('Error initializing app:', error);
                showError('Failed to load content. Please refresh the page.');
            } finally {
                hideLoadingState();
            }
        });
    </script>
</body>
</html>

2025-06-23 19:16:54,328 INFO ipython tables = frappe.db.sql("SHOW TABLES LIKE '%blog%'", as_dict=True)
2025-06-23 19:16:54,329 INFO ipython print(f"\nTables with 'blog' in name:")
2025-06-23 19:16:54,330 INFO ipython for table in tables:
        table_name = list(table.values())[0]
            print(f"- {table_name}")
2025-06-23 19:16:54,330 INFO ipython === session end ===
2025-06-23 19:17:13,494 INFO ipython === bench console session ===
2025-06-23 19:17:13,494 INFO ipython # Check table structure
2025-06-23 19:17:13,495 INFO ipython tables = frappe.db.sql("SHOW TABLES LIKE '%blog%'", as_dict=True)
2025-06-23 19:17:13,495 INFO ipython print("Tables with 'blog' in name:")
2025-06-23 19:17:13,495 INFO ipython for table in tables:
        table_name = list(table.values())[0]
            print(f"- {table_name}")
2025-06-23 19:17:13,495 INFO ipython # Check if DMSOKO Blog Post table exists
2025-06-23 19:17:13,495 INFO ipython dmsoko_table_exists = frappe.db.sql("SHOW TABLES LIKE 'tabDMSOKO Blog Post'", as_dict=True)
2025-06-23 19:17:13,496 INFO ipython print(f"\nDMSOKO Blog Post table exists: {len(dmsoko_table_exists) > 0}")
2025-06-23 19:17:13,496 INFO ipython if len(dmsoko_table_exists) > 0:
        # Check table structure
            columns = frappe.db.sql("DESCRIBE `tabDMSOKO Blog Post`", as_dict=True)
                print("DMSOKO Blog Post table columns:")
2025-06-23 19:17:13,496 INFO ipython     for col in columns[:5]:  # Show first 5 columns
            print(f"- {col['Field']}: {col['Type']}")
        
2025-06-23 19:17:13,496 INFO ipython === session end ===
2025-06-23 19:17:31,918 INFO ipython === bench console session ===
2025-06-23 19:17:31,919 INFO ipython try:
        # Create a simple blog post
            blog_post = frappe.get_doc({
                    "doctype": "DMSOKO Blog Post",
                            "title": "Test Blog Post",
                                    "author": "Test Author",
                                            "blog_intro": "This is a test blog post",
                                                    "content": "Test content",
                                                            "published": 1,
                                                                    "featured": 1
                                                                        })
                                                                            blog_post.insert()
2025-06-23 19:17:31,919 INFO ipython     print(f"Successfully created: {blog_post.name}")
2025-06-23 19:17:31,919 INFO ipython     # Check if it exists
2025-06-23 19:17:31,919 INFO ipython     posts = frappe.get_all("DMSOKO Blog Post", fields=["name", "title", "published"])
2025-06-23 19:17:31,920 INFO ipython     print(f"Found {len(posts)} DMSOKO Blog Posts")
2025-06-23 19:17:31,920 INFO ipython     for post in posts:
            print(f"- {post.name}: {post.title} (published: {post.published})")
                
2025-06-23 19:17:31,920 INFO ipython except Exception as e:
        print(f"Error: {e}")
2025-06-23 19:17:31,920 INFO ipython === session end ===
2025-06-24 08:02:34,767 INFO ipython === bench console session ===
2025-06-24 08:02:34,768 INFO ipython # Create sample blog posts using the standard Blog Post DocType
2025-06-24 08:02:34,768 INFO ipython # First, let's create a blogger and blog category
2025-06-24 08:02:34,768 INFO ipython # Create a blogger
2025-06-24 08:02:34,768 INFO ipython blogger = frappe.get_doc({
    "doctype": "Blogger",
        "full_name": "DMSOKO Team",
            "short_name": "dmsoko-team"
            })
2025-06-24 08:02:34,769 INFO ipython blogger.insert()
2025-06-24 08:02:34,769 INFO ipython print(f"Created blogger: {blogger.name}")
2025-06-24 08:02:34,769 INFO ipython # Create a blog category
2025-06-24 08:02:34,769 INFO ipython blog_category = frappe.get_doc({
    "doctype": "Blog Category",
        "title": "Marketplace Tips",
            "route": "marketplace-tips"
            })
2025-06-24 08:02:34,769 INFO ipython blog_category.insert()
2025-06-24 08:02:34,769 INFO ipython print(f"Created blog category: {blog_category.name}")
2025-06-24 08:02:34,769 INFO ipython === session end ===
2025-06-24 08:03:00,089 INFO ipython === bench console session ===
2025-06-24 08:03:00,089 INFO ipython # Create blog posts
2025-06-24 08:03:00,089 INFO ipython blog_post1 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "Welcome to DMSOKO Blog",
            "blogger": "dmsoko-team",
                "blog_category": "marketplace-tips",
                    "blog_intro": "Welcome to our new blog where we share insights about classified listings and marketplace trends.",
                        "content": "This is our first blog post on the DMSOKO platform. We're excited to share valuable content with our community about buying, selling, and trading in the digital marketplace.",
                            "published": 1,
                                "featured": 1,
                                    "published_on": "2025-06-23",
                                        "route": "welcome-to-dmsoko-blog"
                                        })
2025-06-24 08:03:00,090 INFO ipython blog_post1.insert()
2025-06-24 08:03:00,090 INFO ipython print(f"Created blog post: {blog_post1.name}")
2025-06-24 08:03:00,090 INFO ipython blog_post2 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "Tips for Selling Your Items Online",
            "blogger": "dmsoko-team",
                "blog_category": "marketplace-tips",
                    "blog_intro": "Learn the best practices for creating compelling listings that attract buyers.",
                        "content": "Selling items online can be challenging, but with the right approach, you can maximize your success. Here are some key tips: 1. Take high-quality photos, 2. Write detailed descriptions, 3. Price competitively, 4. Respond quickly to inquiries.",
                            "published": 1,
                                "featured": 0,
                                    "published_on": "2025-06-22",
                                        "route": "tips-for-selling-your-items-online"
                                        })
2025-06-24 08:03:00,090 INFO ipython blog_post2.insert()
2025-06-24 08:03:00,090 INFO ipython print(f"Created blog post: {blog_post2.name}")
2025-06-24 08:03:00,091 INFO ipython blog_post3 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "How to Stay Safe While Trading Online",
            "blogger": "dmsoko-team",
                "blog_category": "marketplace-tips",
                    "blog_intro": "Safety tips for buyers and sellers in online marketplaces.",
                        "content": "Online trading requires caution and awareness. Always meet in public places, verify the identity of the other party, use secure payment methods, and trust your instincts. If something feels wrong, don't proceed with the transaction.",
                            "published": 1,
                                "featured": 1,
                                    "published_on": "2025-06-21",
                                        "route": "how-to-stay-safe-while-trading-online"
                                        })
2025-06-24 08:03:00,091 INFO ipython blog_post3.insert()
2025-06-24 08:03:00,091 INFO ipython print(f"Created blog post: {blog_post3.name}")
2025-06-24 08:03:00,091 INFO ipython === session end ===
2025-06-24 08:11:16,486 INFO ipython === bench console session ===
2025-06-24 08:11:16,486 INFO ipython # Check if blogger and blog category exist
2025-06-24 08:11:16,486 INFO ipython bloggers = frappe.get_all("Blogger", fields=["name", "full_name"])
2025-06-24 08:11:16,487 INFO ipython print("Existing bloggers:")
2025-06-24 08:11:16,487 INFO ipython for blogger in bloggers:
        print(f"- {blogger.name}: {blogger.full_name}")
        
2025-06-24 08:11:16,487 INFO ipython blog_categories = frappe.get_all("Blog Category", fields=["name", "title"])
2025-06-24 08:11:16,487 INFO ipython print("\nExisting blog categories:")
2025-06-24 08:11:16,488 INFO ipython for cat in blog_categories:
        print(f"- {cat.name}: {cat.title}")
        
2025-06-24 08:11:16,488 INFO ipython # Create simple blog posts without blogger and category requirements
2025-06-24 08:11:16,488 INFO ipython blog_post1 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "Welcome to DMSOKO Blog",
            "blog_intro": "Welcome to our new blog where we share insights about classified listings and marketplace trends.",
                "content": "This is our first blog post on the DMSOKO platform. We're excited to share valuable content with our community about buying, selling, and trading in the digital marketplace.",
                    "published": 1,
                        "featured": 1,
                            "published_on": "2025-06-23",
                                "route": "welcome-to-dmsoko-blog"
                                })
2025-06-24 08:11:16,488 INFO ipython blog_post1.insert(ignore_links=True)
2025-06-24 08:11:16,488 INFO ipython print(f"\nCreated blog post: {blog_post1.name}")
2025-06-24 08:11:16,489 INFO ipython === session end ===
2025-06-24 08:11:39,025 INFO ipython === bench console session ===
2025-06-24 08:11:39,026 INFO ipython # Create blog posts without featured flag
2025-06-24 08:11:39,026 INFO ipython blog_post1 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "Welcome to DMSOKO Blog",
            "blog_intro": "Welcome to our new blog where we share insights about classified listings and marketplace trends.",
                "content": "This is our first blog post on the DMSOKO platform. We're excited to share valuable content with our community about buying, selling, and trading in the digital marketplace.",
                    "published": 1,
                        "featured": 0,
                            "published_on": "2025-06-23",
                                "route": "welcome-to-dmsoko-blog"
                                })
2025-06-24 08:11:39,027 INFO ipython blog_post1.insert(ignore_links=True)
2025-06-24 08:11:39,027 INFO ipython print(f"Created blog post: {blog_post1.name}")
2025-06-24 08:11:39,027 INFO ipython blog_post2 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "Tips for Selling Your Items Online",
            "blog_intro": "Learn the best practices for creating compelling listings that attract buyers.",
                "content": "Selling items online can be challenging, but with the right approach, you can maximize your success. Here are some key tips: 1. Take high-quality photos, 2. Write detailed descriptions, 3. Price competitively, 4. Respond quickly to inquiries.",
                    "published": 1,
                        "featured": 0,
                            "published_on": "2025-06-22",
                                "route": "tips-for-selling-your-items-online"
                                })
2025-06-24 08:11:39,027 INFO ipython blog_post2.insert(ignore_links=True)
2025-06-24 08:11:39,028 INFO ipython print(f"Created blog post: {blog_post2.name}")
2025-06-24 08:11:39,028 INFO ipython blog_post3 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "How to Stay Safe While Trading Online",
            "blog_intro": "Safety tips for buyers and sellers in online marketplaces.",
                "content": "Online trading requires caution and awareness. Always meet in public places, verify the identity of the other party, use secure payment methods, and trust your instincts. If something feels wrong, don't proceed with the transaction.",
                    "published": 1,
                        "featured": 0,
                            "published_on": "2025-06-21",
                                "route": "how-to-stay-safe-while-trading-online"
                                })
2025-06-24 08:11:39,028 INFO ipython blog_post3.insert(ignore_links=True)
2025-06-24 08:11:39,028 INFO ipython print(f"Created blog post: {blog_post3.name}")
2025-06-24 08:11:39,028 INFO ipython === session end ===
2025-06-24 08:12:00,364 INFO ipython === bench console session ===
2025-06-24 08:12:00,365 INFO ipython # Create blogger and blog category first
2025-06-24 08:12:00,365 INFO ipython try:
        blogger = frappe.get_doc({
                "doctype": "Blogger",
                        "full_name": "DMSOKO Team",
                                "short_name": "dmsoko-team"
                                    })
                                        blogger.insert(ignore_mandatory=True)
2025-06-24 08:12:00,365 INFO ipython     print(f"Created blogger: {blogger.name}")
2025-06-24 08:12:00,366 INFO ipython except Exception as e:
        print(f"Blogger creation failed: {e}")
2025-06-24 08:12:00,366 INFO ipython try:
        blog_category = frappe.get_doc({
                "doctype": "Blog Category",
                        "title": "Marketplace Tips",
                                "route": "marketplace-tips"
                                    })
                                        blog_category.insert(ignore_mandatory=True)
2025-06-24 08:12:00,366 INFO ipython     print(f"Created blog category: {blog_category.name}")
2025-06-24 08:12:00,366 INFO ipython except Exception as e:
        print(f"Blog category creation failed: {e}")
2025-06-24 08:12:00,367 INFO ipython # Now create blog posts with the required fields
2025-06-24 08:12:00,367 INFO ipython try:
        blog_post1 = frappe.get_doc({
                "doctype": "Blog Post",
                        "title": "Welcome to DMSOKO Blog",
                                "blogger": "dmsoko-team",
                                        "blog_category": "marketplace-tips",
                                                "blog_intro": "Welcome to our new blog where we share insights about classified listings and marketplace trends.",
                                                        "content": "This is our first blog post on the DMSOKO platform. We're excited to share valuable content with our community about buying, selling, and trading in the digital marketplace.",
                                                                "published": 1,
                                                                        "featured": 0,
                                                                                "published_on": "2025-06-23",
                                                                                        "route": "welcome-to-dmsoko-blog"
                                                                                            })
                                                                                                blog_post1.insert(ignore_mandatory=True)
2025-06-24 08:12:00,367 INFO ipython     print(f"Created blog post: {blog_post1.name}")
2025-06-24 08:12:00,367 INFO ipython except Exception as e:
        print(f"Blog post creation failed: {e}")
2025-06-24 08:12:00,367 INFO ipython === session end ===
2025-06-24 08:12:20,271 INFO ipython === bench console session ===
2025-06-24 08:12:20,271 INFO ipython blogger = frappe.get_doc({"doctype": "Blogger", "full_name": "DMSOKO Team", "short_name": "dmsoko-team"})
2025-06-24 08:12:20,271 INFO ipython blogger.insert()
2025-06-24 08:12:20,272 INFO ipython print(f"Created blogger: {blogger.name}")
2025-06-24 08:12:20,272 INFO ipython blog_category = frappe.get_doc({"doctype": "Blog Category", "title": "Marketplace Tips", "route": "marketplace-tips"})
2025-06-24 08:12:20,272 INFO ipython blog_category.insert()
2025-06-24 08:12:20,272 INFO ipython print(f"Created blog category: {blog_category.name}")
2025-06-24 08:12:20,272 INFO ipython blog_post1 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "Welcome to DMSOKO Blog",
            "blogger": "dmsoko-team",
                "blog_category": "marketplace-tips",
                    "blog_intro": "Welcome to our new blog where we share insights about classified listings and marketplace trends.",
                        "content": "This is our first blog post on the DMSOKO platform.",
                            "published": 1,
                                "featured": 0,
                                    "published_on": "2025-06-23",
                                        "route": "welcome-to-dmsoko-blog"
                                        })
2025-06-24 08:12:20,272 INFO ipython blog_post1.insert()
2025-06-24 08:12:20,273 INFO ipython print(f"Created blog post: {blog_post1.name}")
2025-06-24 08:12:20,273 INFO ipython === session end ===
2025-06-24 08:12:44,467 INFO ipython === bench console session ===
2025-06-24 08:12:44,467 INFO ipython # Create more blog posts
2025-06-24 08:12:44,468 INFO ipython blog_post2 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "Tips for Selling Your Items Online",
            "blogger": "dmsoko-team",
                "blog_category": "marketplace-tips",
                    "blog_intro": "Learn the best practices for creating compelling listings that attract buyers.",
                        "content": "Selling items online can be challenging, but with the right approach, you can maximize your success.",
                            "published": 1,
                                "featured": 1,
                                    "published_on": "2025-06-22",
                                        "route": "tips-for-selling-your-items-online",
                                            "meta_image": "/assets/dmsoko/images/placeholder.svg"
                                            })
2025-06-24 08:12:44,468 INFO ipython blog_post2.insert()
2025-06-24 08:12:44,468 INFO ipython print(f"Created blog post: {blog_post2.name}")
2025-06-24 08:12:44,469 INFO ipython blog_post3 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "How to Stay Safe While Trading Online",
            "blogger": "dmsoko-team",
                "blog_category": "marketplace-tips",
                    "blog_intro": "Safety tips for buyers and sellers in online marketplaces.",
                        "content": "Online trading requires caution and awareness. Always meet in public places.",
                            "published": 1,
                                "featured": 1,
                                    "published_on": "2025-06-21",
                                        "route": "how-to-stay-safe-while-trading-online",
                                            "meta_image": "/assets/dmsoko/images/placeholder.svg"
                                            })
2025-06-24 08:12:44,469 INFO ipython blog_post3.insert()
2025-06-24 08:12:44,469 INFO ipython print(f"Created blog post: {blog_post3.name}")
2025-06-24 08:12:44,469 INFO ipython # Test the API
2025-06-24 08:12:44,470 INFO ipython from dmsoko.api.blog import get_featured_blog_posts, get_blog_posts
2025-06-24 08:12:44,470 INFO ipython featured_result = get_featured_blog_posts(limit=3)
2025-06-24 08:12:44,470 INFO ipython print(f"\nFeatured blog posts: {len(featured_result['data'])} found")
2025-06-24 08:12:44,470 INFO ipython for post in featured_result['data']:
        print(f"- {post['title']}")
        
2025-06-24 08:12:44,470 INFO ipython all_posts_result = get_blog_posts(limit=5, page=1)
2025-06-24 08:12:44,471 INFO ipython print(f"\nAll blog posts: {len(all_posts_result['data'])} found")
2025-06-24 08:12:44,471 INFO ipython for post in all_posts_result['data']:
        print(f"- {post['title']}")
        
2025-06-24 08:12:44,471 INFO ipython === session end ===
2025-06-24 08:13:02,569 INFO ipython === bench console session ===
2025-06-24 08:13:02,570 INFO ipython # Check what blog posts exist
2025-06-24 08:13:02,570 INFO ipython blog_posts = frappe.get_all("Blog Post", fields=["name", "title", "published", "featured"])
2025-06-24 08:13:02,570 INFO ipython print(f"Existing blog posts: {len(blog_posts)}")
2025-06-24 08:13:02,570 INFO ipython for post in blog_posts:
        print(f"- {post.name}: {post.title} (published: {post.published}, featured: {post.featured})")
        
2025-06-24 08:13:02,570 INFO ipython # Test the API with existing posts
2025-06-24 08:13:02,571 INFO ipython from dmsoko.api.blog import get_blog_posts, get_featured_blog_posts
2025-06-24 08:13:02,571 INFO ipython all_posts_result = get_blog_posts(limit=5, page=1)
2025-06-24 08:13:02,571 INFO ipython print(f"\nAPI result - All blog posts: {len(all_posts_result['data'])} found")
2025-06-24 08:13:02,571 INFO ipython print(f"Total: {all_posts_result['total']}")
2025-06-24 08:13:02,571 INFO ipython # Check if we have any published posts
2025-06-24 08:13:02,572 INFO ipython published_posts = frappe.get_all("Blog Post", filters={"published": 1}, fields=["name", "title"])
2025-06-24 08:13:02,572 INFO ipython print(f"\nPublished posts: {len(published_posts)}")
2025-06-24 08:13:02,572 INFO ipython for post in published_posts:
        print(f"- {post.name}: {post.title}")
        
2025-06-24 08:13:02,572 INFO ipython === session end ===
2025-06-24 17:58:55,247 INFO ipython === bench console session ===
2025-06-24 17:58:55,250 INFO ipython # Create some sample featured listings to test the carousel
2025-06-24 17:58:55,252 INFO ipython import frappe
2025-06-24 17:58:55,253 INFO ipython # Create sample listings with featured flag
2025-06-24 17:58:55,253 INFO ipython for i in range(1, 8):
        listing = frappe.get_doc({
                "doctype": "Listing",
                        "title": f"Featured Item {i}",
                                "description": f"This is a featured listing item number {i} for testing the carousel functionality.",
                                        "price": 100 + (i * 50),
                                                "currency": "USD",
                                                        "location": f"City {i}",
                                                                "category": "Electronics",  # Assuming this category exists
                                                                        "listing_type": "For Sale",
                                                                                "condition": "New",
                                                                                        "status": "Active",
                                                                                                "featured": 1,
                                                                                                        "expires_on": "2025-12-31",
                                                                                                                "created_by_user": "Administrator"
                                                                                                                    })
                                                                                                                        listing.insert(ignore_permissions=True)
2025-06-24 17:58:55,254 INFO ipython     print(f"Created featured listing: {listing.name}")
2025-06-24 17:58:55,254 INFO ipython print("Sample featured listings created successfully!")
2025-06-24 17:58:55,255 INFO ipython === session end ===
2025-06-24 17:59:22,737 INFO ipython === bench console session ===
2025-06-24 17:59:22,737 INFO ipython import frappe
2025-06-24 17:59:22,738 INFO ipython # Create a simple featured listing
2025-06-24 17:59:22,738 INFO ipython listing = frappe.get_doc({
    "doctype": "Listing",
        "title": "Featured iPhone 15",
            "description": "Brand new iPhone 15 in excellent condition",
                "price": 999,
                    "currency": "USD",
                        "location": "New York",
                            "category": "Electronics",
                                "listing_type": "For Sale",
                                    "condition": "New",
                                        "status": "Active",
                                            "featured": 1,
                                                "expires_on": "2025-12-31",
                                                    "created_by_user": "Administrator"
                                                    })
2025-06-24 17:59:22,738 INFO ipython listing.insert(ignore_permissions=True)
2025-06-24 17:59:22,738 INFO ipython print(f"Created featured listing: {listing.name}")
2025-06-24 17:59:22,738 INFO ipython === session end ===
2025-06-24 17:59:57,617 INFO ipython === bench console session ===
2025-06-24 17:59:57,617 INFO ipython import frappe
2025-06-24 17:59:57,618 INFO ipython # Create multiple featured listings
2025-06-24 17:59:57,618 INFO ipython listings_data = [
    {"title": "MacBook Pro 16", "price": 2499, "location": "San Francisco"},
        {"title": "Gaming Chair", "price": 299, "location": "Los Angeles"},
            {"title": "Vintage Camera", "price": 450, "location": "Chicago"},
                {"title": "Electric Bike", "price": 1200, "location": "Seattle"},
                    {"title": "Designer Watch", "price": 800, "location": "Miami"}
                    ]
2025-06-24 17:59:57,618 INFO ipython for data in listings_data:
        listing = frappe.get_doc({
                "doctype": "Listing",
                        "title": data["title"],
                                "description": f"High quality {data['title']} in excellent condition",
                                        "price": data["price"],
                                                "currency": "USD",
                                                        "location": data["location"],
                                                                "category": "Electronics",
                                                                        "listing_type": "For Sale",
                                                                                "condition": "New",
                                                                                        "status": "Active",
                                                                                                "featured": 1,
                                                                                                        "expires_on": "2025-12-31",
                                                                                                                "created_by_user": "Administrator"
                                                                                                                    })
                                                                                                                        listing.insert(ignore_permissions=True)
2025-06-24 17:59:57,618 INFO ipython     print(f"Created: {listing.name} - {listing.title}")
2025-06-24 17:59:57,619 INFO ipython print("All featured listings created!")
2025-06-24 17:59:57,619 INFO ipython === session end ===

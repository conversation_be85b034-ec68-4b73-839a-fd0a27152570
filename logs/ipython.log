2025-06-23 19:16:54,328 INFO ipython tables = frappe.db.sql("SHOW TABLES LIKE '%blog%'", as_dict=True)
2025-06-23 19:16:54,329 INFO ipython print(f"\nTables with 'blog' in name:")
2025-06-23 19:16:54,330 INFO ipython for table in tables:
        table_name = list(table.values())[0]
            print(f"- {table_name}")
2025-06-23 19:16:54,330 INFO ipython === session end ===
2025-06-23 19:17:13,494 INFO ipython === bench console session ===
2025-06-23 19:17:13,494 INFO ipython # Check table structure
2025-06-23 19:17:13,495 INFO ipython tables = frappe.db.sql("SHOW TABLES LIKE '%blog%'", as_dict=True)
2025-06-23 19:17:13,495 INFO ipython print("Tables with 'blog' in name:")
2025-06-23 19:17:13,495 INFO ipython for table in tables:
        table_name = list(table.values())[0]
            print(f"- {table_name}")
2025-06-23 19:17:13,495 INFO ipython # Check if DMSOKO Blog Post table exists
2025-06-23 19:17:13,495 INFO ipython dmsoko_table_exists = frappe.db.sql("SHOW TABLES LIKE 'tabDMSOKO Blog Post'", as_dict=True)
2025-06-23 19:17:13,496 INFO ipython print(f"\nDMSOKO Blog Post table exists: {len(dmsoko_table_exists) > 0}")
2025-06-23 19:17:13,496 INFO ipython if len(dmsoko_table_exists) > 0:
        # Check table structure
            columns = frappe.db.sql("DESCRIBE `tabDMSOKO Blog Post`", as_dict=True)
                print("DMSOKO Blog Post table columns:")
2025-06-23 19:17:13,496 INFO ipython     for col in columns[:5]:  # Show first 5 columns
            print(f"- {col['Field']}: {col['Type']}")
        
2025-06-23 19:17:13,496 INFO ipython === session end ===
2025-06-23 19:17:31,918 INFO ipython === bench console session ===
2025-06-23 19:17:31,919 INFO ipython try:
        # Create a simple blog post
            blog_post = frappe.get_doc({
                    "doctype": "DMSOKO Blog Post",
                            "title": "Test Blog Post",
                                    "author": "Test Author",
                                            "blog_intro": "This is a test blog post",
                                                    "content": "Test content",
                                                            "published": 1,
                                                                    "featured": 1
                                                                        })
                                                                            blog_post.insert()
2025-06-23 19:17:31,919 INFO ipython     print(f"Successfully created: {blog_post.name}")
2025-06-23 19:17:31,919 INFO ipython     # Check if it exists
2025-06-23 19:17:31,919 INFO ipython     posts = frappe.get_all("DMSOKO Blog Post", fields=["name", "title", "published"])
2025-06-23 19:17:31,920 INFO ipython     print(f"Found {len(posts)} DMSOKO Blog Posts")
2025-06-23 19:17:31,920 INFO ipython     for post in posts:
            print(f"- {post.name}: {post.title} (published: {post.published})")
                
2025-06-23 19:17:31,920 INFO ipython except Exception as e:
        print(f"Error: {e}")
2025-06-23 19:17:31,920 INFO ipython === session end ===
2025-06-24 08:02:34,767 INFO ipython === bench console session ===
2025-06-24 08:02:34,768 INFO ipython # Create sample blog posts using the standard Blog Post DocType
2025-06-24 08:02:34,768 INFO ipython # First, let's create a blogger and blog category
2025-06-24 08:02:34,768 INFO ipython # Create a blogger
2025-06-24 08:02:34,768 INFO ipython blogger = frappe.get_doc({
    "doctype": "Blogger",
        "full_name": "DMSOKO Team",
            "short_name": "dmsoko-team"
            })
2025-06-24 08:02:34,769 INFO ipython blogger.insert()
2025-06-24 08:02:34,769 INFO ipython print(f"Created blogger: {blogger.name}")
2025-06-24 08:02:34,769 INFO ipython # Create a blog category
2025-06-24 08:02:34,769 INFO ipython blog_category = frappe.get_doc({
    "doctype": "Blog Category",
        "title": "Marketplace Tips",
            "route": "marketplace-tips"
            })
2025-06-24 08:02:34,769 INFO ipython blog_category.insert()
2025-06-24 08:02:34,769 INFO ipython print(f"Created blog category: {blog_category.name}")
2025-06-24 08:02:34,769 INFO ipython === session end ===
2025-06-24 08:03:00,089 INFO ipython === bench console session ===
2025-06-24 08:03:00,089 INFO ipython # Create blog posts
2025-06-24 08:03:00,089 INFO ipython blog_post1 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "Welcome to DMSOKO Blog",
            "blogger": "dmsoko-team",
                "blog_category": "marketplace-tips",
                    "blog_intro": "Welcome to our new blog where we share insights about classified listings and marketplace trends.",
                        "content": "This is our first blog post on the DMSOKO platform. We're excited to share valuable content with our community about buying, selling, and trading in the digital marketplace.",
                            "published": 1,
                                "featured": 1,
                                    "published_on": "2025-06-23",
                                        "route": "welcome-to-dmsoko-blog"
                                        })
2025-06-24 08:03:00,090 INFO ipython blog_post1.insert()
2025-06-24 08:03:00,090 INFO ipython print(f"Created blog post: {blog_post1.name}")
2025-06-24 08:03:00,090 INFO ipython blog_post2 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "Tips for Selling Your Items Online",
            "blogger": "dmsoko-team",
                "blog_category": "marketplace-tips",
                    "blog_intro": "Learn the best practices for creating compelling listings that attract buyers.",
                        "content": "Selling items online can be challenging, but with the right approach, you can maximize your success. Here are some key tips: 1. Take high-quality photos, 2. Write detailed descriptions, 3. Price competitively, 4. Respond quickly to inquiries.",
                            "published": 1,
                                "featured": 0,
                                    "published_on": "2025-06-22",
                                        "route": "tips-for-selling-your-items-online"
                                        })
2025-06-24 08:03:00,090 INFO ipython blog_post2.insert()
2025-06-24 08:03:00,090 INFO ipython print(f"Created blog post: {blog_post2.name}")
2025-06-24 08:03:00,091 INFO ipython blog_post3 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "How to Stay Safe While Trading Online",
            "blogger": "dmsoko-team",
                "blog_category": "marketplace-tips",
                    "blog_intro": "Safety tips for buyers and sellers in online marketplaces.",
                        "content": "Online trading requires caution and awareness. Always meet in public places, verify the identity of the other party, use secure payment methods, and trust your instincts. If something feels wrong, don't proceed with the transaction.",
                            "published": 1,
                                "featured": 1,
                                    "published_on": "2025-06-21",
                                        "route": "how-to-stay-safe-while-trading-online"
                                        })
2025-06-24 08:03:00,091 INFO ipython blog_post3.insert()
2025-06-24 08:03:00,091 INFO ipython print(f"Created blog post: {blog_post3.name}")
2025-06-24 08:03:00,091 INFO ipython === session end ===

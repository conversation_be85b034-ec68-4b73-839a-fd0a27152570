2025-06-20 08:22:45,368 INFO ipython === bench console session ===
2025-06-20 08:22:45,369 INFO ipython from frappe.core.doctype.data_import.data_import import import_doc
2025-06-20 08:22:45,369 INFO ipython import_doc("apps/dmsoko/dmsoko/fixtures/custom_role.json")
2025-06-20 08:22:45,370 INFO ipython import_doc("/home/<USER>/dev/personal/apps/dmsoko/dmsoko/fixtures/custom_role.json")
2025-06-20 08:22:45,370 INFO ipython import_doc("/home/<USER>/dev/personal/apps/dmsoko/dmsoko/fixtures/category.json")
2025-06-20 08:22:45,370 INFO ipython === session end ===
2025-06-20 08:25:52,010 INFO ipython === bench console session ===
2025-06-20 08:25:52,010 INFO ipython frappe.db.exists("DocType", "Category")
2025-06-20 08:25:52,011 INFO ipython print(frappe.db.exists("DocType", "Category"))
2025-06-20 08:25:52,011 INFO ipython frappe.get_all("DocType", filters={"module": "Listings"}, fields=["name"])
2025-06-20 08:25:52,011 INFO ipython frappe.get_installed_apps()
2025-06-20 08:25:52,011 INFO ipython === session end ===
2025-06-20 08:27:56,703 INFO ipython === bench console session ===
2025-06-20 08:27:56,703 INFO ipython print(frappe.db.exists("DocType", "Category"))
2025-06-20 08:27:56,703 INFO ipython frappe.get_all("DocType", filters={"app": "dmsoko"}, fields=["name", "module"])
2025-06-20 08:27:56,704 INFO ipython === session end ===
2025-06-20 09:02:36,533 INFO ipython === bench console session ===
2025-06-20 09:02:36,534 INFO ipython from frappe.modules.import_file import import_file_by_path
2025-06-20 09:02:36,534 INFO ipython import os
2025-06-20 09:02:36,534 INFO ipython # Import Category DocType
2025-06-20 09:02:36,534 INFO ipython try:
        import_file_by_path("/home/<USER>/dev/personal/apps/dmsoko/dmsoko/listings/doctype/category/category.json", force=True)
            print("✅ Category DocType imported successfully")
2025-06-20 09:02:36,534 INFO ipython except Exception as e:
        print(f"❌ Error importing Category: {e}")
2025-06-20 09:02:36,534 INFO ipython try:
        import_file_by_path("/home/<USER>/dev/personal/apps/dmsoko/dmsoko/listings/doctype/category/category.json", force=True)
            print("✅ Category DocType imported successfully")
2025-06-20 09:02:36,534 INFO ipython except Exception as e:
        print(f"❌ Error importing Category: {e}")
2025-06-20 09:02:36,535 INFO ipython === session end ===
2025-06-20 09:14:24,606 INFO ipython === bench console session ===
2025-06-20 09:14:24,606 INFO ipython print("Checking DMSoko DocTypes...")
2025-06-20 09:14:24,607 INFO ipython dmsoko_modules = ["Listings", "Messaging", "User Management"]
2025-06-20 09:14:24,607 INFO ipython for module in dmsoko_modules:
        doctypes = frappe.get_all("DocType", filters={"module": module}, fields=["name", "module"])
            print(f"\n{module} Module:")
2025-06-20 09:14:24,607 INFO ipython     if doctypes:
            for dt in doctypes:
                        print(f"  ✅ {dt.name}")
                        else:
                                print(f"  ❌ No DocTypes found")
2025-06-20 09:14:24,607 INFO ipython for module in ["Listings", "Messaging", "User Management"]:
        doctypes = frappe.get_all("DocType", filters={"module": module}, fields=["name", "module"])
            print(f"\n{module} Module:")
2025-06-20 09:14:24,608 INFO ipython     if doctypes:
            for dt in doctypes:
                        print(f"  ✅ {dt.name}")
                        else:
                                print(f"  ❌ No DocTypes found")
2025-06-20 09:14:24,608 INFO ipython frappe.get_all("DocType", filters={"module": "Listings"}, fields=["name"])
2025-06-20 09:14:24,608 INFO ipython frappe.get_all("DocType", filters={"module": "Messaging"}, fields=["name"])
2025-06-20 09:14:24,608 INFO ipython frappe.get_all("DocType", filters={"module": "User Management"}, fields=["name"])
2025-06-20 09:14:24,608 INFO ipython === session end ===
2025-06-20 10:33:31,963 INFO ipython === bench console session ===
2025-06-20 10:33:31,972 INFO ipython import frappe
2025-06-20 10:33:31,973 INFO ipython frappe.db.get_list("Listing", fields=["name", "title", "status"])
2025-06-20 10:33:31,973 INFO ipython frappe.db.get_list("Category", fields=["name", "category_name"])
2025-06-20 10:33:31,973 INFO ipython === session end ===
2025-06-20 10:38:30,374 INFO ipython === bench console session ===
2025-06-20 10:38:30,374 INFO ipython import frappe
2025-06-20 10:38:30,375 INFO ipython import random
2025-06-20 10:38:30,375 INFO ipython from datetime import datetime, timedelta
2025-06-20 10:38:30,375 INFO ipython # Sample listing data
2025-06-20 10:38:30,375 INFO ipython listings_data = [
    {
            "title": "iPhone 14 Pro Max - Excellent Condition",
                    "description": "Selling my iPhone 14 Pro Max in excellent condition. Used for only 6 months. Comes with original box, charger, and screen protector already applied. No scratches or dents. Battery health at 98%.",
                            "category": "Smartphones",
                                    "price": 85000,
                                            "location": "Dar es Salaam",
                                                    "condition": "Like New",
                                                            "contact_phone": "+255 712 345 678",
                                                                    "contact_email": "<EMAIL>"
                                                                        },
                                                                            {
                                                                                    "title": "Toyota Corolla 2018 - Low Mileage",
                                                                                            "description": "Well maintained Toyota Corolla 2018 with only 45,000 km on the odometer. Regular service history available. Non-smoking owner. Perfect for daily commuting.",
                                                                                                    "category": "Cars",
                                                                                                            "price": 28000000,
                                                                                                                    "location": "Arusha",
                                                                                                                            "condition": "Good",
                                                                                                                                    "contact_phone": "+255 754 987 654",
                                                                                                                                            "contact_email": "<EMAIL>"
                                                                                                                                                },
                                                                                                                                                    {
                                                                                                                                                            "title": "3 Bedroom House for Sale - Mikocheni",
                                                                                                                                                                    "description": "Beautiful 3 bedroom house in Mikocheni area. Modern kitchen, spacious living room, 2 bathrooms, and a small garden. Close to schools and shopping centers.",
                                                                                                                                                                            "category": "Houses",
                                                                                                                                                                                    "price": 180000000,
                                                                                                                                                                                            "location": "Dar es Salaam",
                                                                                                                                                                                                    "condition": "Excellent",
                                                                                                                                                                                                            "contact_phone": "+255 765 432 109",
                                                                                                                                                                                                                    "contact_email": "<EMAIL>"
                                                                                                                                                                                                                        }
                                                                                                                                                                                                                        ]
2025-06-20 10:38:30,375 INFO ipython # Create first listing
2025-06-20 10:38:30,375 INFO ipython listing = frappe.new_doc("Listing")
2025-06-20 10:38:30,375 INFO ipython listing.title = "iPhone 14 Pro Max - Excellent Condition"
2025-06-20 10:38:30,375 INFO ipython listing.description = "Selling my iPhone 14 Pro Max in excellent condition. Used for only 6 months. Comes with original box, charger, and screen protector already applied."
2025-06-20 10:38:30,376 INFO ipython listing.category = "Smartphones"
2025-06-20 10:38:30,376 INFO ipython listing.price = 85000
2025-06-20 10:38:30,376 INFO ipython listing.location = "Dar es Salaam"
2025-06-20 10:38:30,376 INFO ipython listing.condition = "Like New"
2025-06-20 10:38:30,376 INFO ipython listing.contact_phone = "+255 712 345 678"
2025-06-20 10:38:30,377 INFO ipython listing.contact_email = "<EMAIL>"
2025-06-20 10:38:30,377 INFO ipython listing.status = "Active"
2025-06-20 10:38:30,377 INFO ipython listing.listing_type = "For Sale"
2025-06-20 10:38:30,377 INFO ipython listing.insert(ignore_permissions=True)
2025-06-20 10:38:30,377 INFO ipython print(f"Created: {listing.name}")
2025-06-20 10:38:30,377 INFO ipython # Fix the condition and try again
2025-06-20 10:38:30,378 INFO ipython listing.condition = "Used"
2025-06-20 10:38:30,378 INFO ipython listing.save(ignore_permissions=True)
2025-06-20 10:38:30,378 INFO ipython print(f"Created: {listing.name}")
2025-06-20 10:38:30,378 INFO ipython # Create more listings
2025-06-20 10:38:30,378 INFO ipython listings = [
    {"title": "Toyota Corolla 2018 - Low Mileage", "description": "Well maintained Toyota Corolla 2018 with only 45,000 km on the odometer.", "category": "Cars", "price": 28000000, "location": "Arusha", "condition": "Used"},
        {"title": "3 Bedroom House for Sale - Mikocheni", "description": "Beautiful 3 bedroom house in Mikocheni area. Modern kitchen, spacious living room.", "category": "Houses", "price": 180000000, "location": "Dar es Salaam", "condition": "Used"},
            {"title": "MacBook Pro M2 - Perfect for Students", "description": "MacBook Pro with M2 chip, 16GB RAM, 512GB SSD. Perfect for students and professionals.", "category": "Laptops & Computers", "price": 2800000, "location": "Mwanza", "condition": "Used"},
                {"title": "Modern Sofa Set - 7 Seater", "description": "Beautiful modern sofa set with 7 seats. Comfortable and stylish, perfect for living room.", "category": "Furniture", "price": 850000, "location": "Dodoma", "condition": "Used"},
                    {"title": "Honda CB 150R - Sport Motorcycle", "description": "Honda CB 150R in excellent condition. Low mileage, well maintained.", "category": "Motorcycles", "price": 4500000, "location": "Mbeya", "condition": "Used"}
                    ]
2025-06-20 10:38:30,378 INFO ipython for data in listings:
        listing = frappe.new_doc("Listing")
            listing.title = data["title"]
2025-06-20 10:38:30,378 INFO ipython     listing.description = data["description"]
2025-06-20 10:38:30,379 INFO ipython     listing.category = data["category"]
2025-06-20 10:38:30,379 INFO ipython     listing.price = data["price"]
2025-06-20 10:38:30,379 INFO ipython     listing.location = data["location"]
2025-06-20 10:38:30,379 INFO ipython     listing.condition = data["condition"]
2025-06-20 10:38:30,379 INFO ipython     listing.contact_phone = "+255 700 000 000"
2025-06-20 10:38:30,379 INFO ipython     listing.contact_email = "<EMAIL>"
2025-06-20 10:38:30,379 INFO ipython     listing.status = "Active"
2025-06-20 10:38:30,380 INFO ipython     listing.listing_type = "For Sale"
2025-06-20 10:38:30,380 INFO ipython     listing.insert(ignore_permissions=True)
2025-06-20 10:38:30,380 INFO ipython     print(f"Created: {listing.name} - {listing.title}")
2025-06-20 10:38:30,380 INFO ipython print("All listings created!")
2025-06-20 10:38:30,380 INFO ipython # Create Toyota listing
2025-06-20 10:38:30,380 INFO ipython listing2 = frappe.new_doc("Listing")
2025-06-20 10:38:30,380 INFO ipython listing2.title = "Toyota Corolla 2018 - Low Mileage"
2025-06-20 10:38:30,380 INFO ipython listing2.description = "Well maintained Toyota Corolla 2018 with only 45,000 km on the odometer. Regular service history available."
2025-06-20 10:38:30,380 INFO ipython listing2.category = "Cars"
2025-06-20 10:38:30,381 INFO ipython listing2.price = 28000000
2025-06-20 10:38:30,381 INFO ipython listing2.location = "Arusha"
2025-06-20 10:38:30,381 INFO ipython listing2.condition = "Used"
2025-06-20 10:38:30,381 INFO ipython listing2.contact_phone = "+255754987654"
2025-06-20 10:38:30,381 INFO ipython listing2.contact_email = "<EMAIL>"
2025-06-20 10:38:30,381 INFO ipython listing2.status = "Active"
2025-06-20 10:38:30,381 INFO ipython listing2.listing_type = "For Sale"
2025-06-20 10:38:30,381 INFO ipython listing2.insert(ignore_permissions=True)
2025-06-20 10:38:30,381 INFO ipython print(f"Created: {listing2.name} - {listing2.title}")
2025-06-20 10:38:30,382 INFO ipython # Create House listing
2025-06-20 10:38:30,382 INFO ipython listing3 = frappe.new_doc("Listing")
2025-06-20 10:38:30,382 INFO ipython listing3.title = "3 Bedroom House for Sale - Mikocheni"
2025-06-20 10:38:30,382 INFO ipython listing3.description = "Beautiful 3 bedroom house in Mikocheni area. Modern kitchen, spacious living room, 2 bathrooms, and a small garden."
2025-06-20 10:38:30,382 INFO ipython listing3.category = "Houses"
2025-06-20 10:38:30,382 INFO ipython listing3.price = 180000000
2025-06-20 10:38:30,382 INFO ipython listing3.location = "Dar es Salaam"
2025-06-20 10:38:30,383 INFO ipython listing3.condition = "Used"
2025-06-20 10:38:30,383 INFO ipython listing3.contact_phone = "+255765432109"
2025-06-20 10:38:30,383 INFO ipython listing3.contact_email = "<EMAIL>"
2025-06-20 10:38:30,383 INFO ipython listing3.status = "Active"
2025-06-20 10:38:30,383 INFO ipython listing3.listing_type = "For Sale"
2025-06-20 10:38:30,383 INFO ipython listing3.insert(ignore_permissions=True)
2025-06-20 10:38:30,383 INFO ipython print(f"Created: {listing3.name} - {listing3.title}")
2025-06-20 10:38:30,383 INFO ipython # Create MacBook listing
2025-06-20 10:38:30,384 INFO ipython listing4 = frappe.new_doc("Listing")
2025-06-20 10:38:30,384 INFO ipython listing4.title = "MacBook Pro M2 - Perfect for Students"
2025-06-20 10:38:30,384 INFO ipython listing4.description = "MacBook Pro with M2 chip, 16GB RAM, 512GB SSD. Perfect for students and professionals. Comes with laptop bag."
2025-06-20 10:38:30,384 INFO ipython listing4.category = "Laptops & Computers"
2025-06-20 10:38:30,384 INFO ipython listing4.price = 2800000
2025-06-20 10:38:30,384 INFO ipython listing4.location = "Mwanza"
2025-06-20 10:38:30,384 INFO ipython listing4.condition = "Used"
2025-06-20 10:38:30,384 INFO ipython listing4.contact_phone = "+255678123456"
2025-06-20 10:38:30,384 INFO ipython listing4.contact_email = "<EMAIL>"
2025-06-20 10:38:30,384 INFO ipython listing4.status = "Active"
2025-06-20 10:38:30,385 INFO ipython listing4.listing_type = "For Sale"
2025-06-20 10:38:30,385 INFO ipython listing4.insert(ignore_permissions=True)
2025-06-20 10:38:30,385 INFO ipython print(f"Created: {listing4.name} - {listing4.title}")
2025-06-20 10:38:30,385 INFO ipython # Create Sofa listing
2025-06-20 10:38:30,385 INFO ipython listing5 = frappe.new_doc("Listing")
2025-06-20 10:38:30,385 INFO ipython listing5.title = "Modern Sofa Set - 7 Seater"
2025-06-20 10:38:30,385 INFO ipython listing5.description = "Beautiful modern sofa set with 7 seats. Comfortable and stylish, perfect for living room. Excellent condition."
2025-06-20 10:38:30,385 INFO ipython listing5.category = "Furniture"
2025-06-20 10:38:30,385 INFO ipython listing5.price = 850000
2025-06-20 10:38:30,386 INFO ipython listing5.location = "Dodoma"
2025-06-20 10:38:30,386 INFO ipython listing5.condition = "Used"
2025-06-20 10:38:30,386 INFO ipython listing5.contact_phone = "+255689765432"
2025-06-20 10:38:30,386 INFO ipython listing5.contact_email = "<EMAIL>"
2025-06-20 10:38:30,386 INFO ipython listing5.status = "Active"
2025-06-20 10:38:30,386 INFO ipython listing5.listing_type = "For Sale"
2025-06-20 10:38:30,386 INFO ipython listing5.insert(ignore_permissions=True)
2025-06-20 10:38:30,386 INFO ipython print(f"Created: {listing5.name} - {listing5.title}")
2025-06-20 10:38:30,386 INFO ipython print("All test listings created successfully!")
2025-06-20 10:38:30,387 INFO ipython # Verify listings were created
2025-06-20 10:38:30,387 INFO ipython listings = frappe.db.get_list("Listing", fields=["name", "title", "price", "location", "status"])
2025-06-20 10:38:30,387 INFO ipython print(f"Total listings created: {len(listings)}")
2025-06-20 10:38:30,387 INFO ipython for listing in listings:
        print(f"- {listing.name}: {listing.title} - {listing.price:,} TZS in {listing.location}")
        
2025-06-20 10:38:30,387 INFO ipython === session end ===
2025-06-20 10:39:33,781 INFO ipython === bench console session ===
2025-06-20 10:39:33,782 INFO ipython import frappe
2025-06-20 10:39:33,782 INFO ipython from datetime import datetime, timedelta
2025-06-20 10:39:33,782 INFO ipython # Update all listings to have expires_on date
2025-06-20 10:39:33,782 INFO ipython listings = frappe.get_all("Listing", fields=["name"])
2025-06-20 10:39:33,782 INFO ipython for listing in listings:
        doc = frappe.get_doc("Listing", listing.name)
            if not doc.expires_on:
                        doc.expires_on = (datetime.now() + timedelta(days=30)).date()
2025-06-20 10:39:33,783 INFO ipython         doc.save(ignore_permissions=True)
2025-06-20 10:39:33,783 INFO ipython         print(f"Updated {listing.name} with expires_on: {doc.expires_on}")
2025-06-20 10:39:33,783 INFO ipython print("All listings updated with expires_on dates")
2025-06-20 10:39:33,783 INFO ipython # Fix the listings one by one
2025-06-20 10:39:33,783 INFO ipython listings = frappe.get_all("Listing", fields=["name"])
2025-06-20 10:39:33,783 INFO ipython for listing in listings:
        doc = frappe.get_doc("Listing", listing.name)
            if not doc.expires_on:
                        doc.expires_on = (datetime.now() + timedelta(days=30)).date()
2025-06-20 10:39:33,783 INFO ipython         doc.save(ignore_permissions=True)
2025-06-20 10:39:33,784 INFO ipython         print(f"Updated {listing.name} with expires_on: {doc.expires_on}")
2025-06-20 10:39:33,784 INFO ipython     else:
            print(f"{listing.name} already has expires_on: {doc.expires_on}")
2025-06-20 10:39:33,784 INFO ipython print("Done updating listings")
2025-06-20 10:39:33,784 INFO ipython # Update listings directly with SQL
2025-06-20 10:39:33,784 INFO ipython frappe.db.sql("UPDATE `tabListing` SET expires_on = DATE_ADD(CURDATE(), INTERVAL 30 DAY) WHERE expires_on IS NULL")
2025-06-20 10:39:33,784 INFO ipython frappe.db.commit()
2025-06-20 10:39:33,784 INFO ipython print("Updated all listings with expires_on dates")
2025-06-20 10:39:33,784 INFO ipython # Test the API
2025-06-20 10:39:33,784 INFO ipython response = frappe.call("dmsoko.api.listings.get_recent_listings", limit=5)
2025-06-20 10:39:33,785 INFO ipython print(f"API Response: {response}")
2025-06-20 10:39:33,785 INFO ipython === session end ===
2025-06-20 10:49:48,559 INFO ipython === bench console session ===
2025-06-20 10:49:48,559 INFO ipython import frappe
2025-06-20 10:49:48,559 INFO ipython from datetime import datetime, timedelta
2025-06-20 10:49:48,559 INFO ipython # Check current listings
2025-06-20 10:49:48,560 INFO ipython listings = frappe.db.sql("SELECT name, title, status, expires_on FROM `tabListing`", as_dict=True)
2025-06-20 10:49:48,560 INFO ipython print(f"Found {len(listings)} listings:")
2025-06-20 10:49:48,560 INFO ipython for listing in listings:
        print(f"- {listing.name}: {listing.title} | Status: {listing.status} | Expires: {listing.expires_on}")
        
2025-06-20 10:49:48,560 INFO ipython # Update expires_on for all listings
2025-06-20 10:49:48,560 INFO ipython today = frappe.utils.today()
2025-06-20 10:49:48,561 INFO ipython future_date = frappe.utils.add_days(today, 30)
2025-06-20 10:49:48,561 INFO ipython frappe.db.sql(f"UPDATE `tabListing` SET expires_on = '{future_date}' WHERE expires_on IS NULL OR expires_on < '{today}'")
2025-06-20 10:49:48,561 INFO ipython frappe.db.commit()
2025-06-20 10:49:48,561 INFO ipython print(f"\nUpdated listings to expire on: {future_date}")
2025-06-20 10:49:48,561 INFO ipython # Test the API
2025-06-20 10:49:48,561 INFO ipython response = frappe.call("dmsoko.api.listings.get_listings", page=1, page_size=5)
2025-06-20 10:49:48,562 INFO ipython print(f"\nAPI Response: {response}")
2025-06-20 10:49:48,562 INFO ipython === session end ===
2025-06-20 10:50:25,757 INFO ipython === bench console session ===
2025-06-20 10:50:25,758 INFO ipython import frappe
2025-06-20 10:50:25,758 INFO ipython from datetime import datetime, timedelta
2025-06-20 10:50:25,758 INFO ipython # Create test listings quickly
2025-06-20 10:50:25,758 INFO ipython listings_data = [
    {
            "title": "iPhone 14 Pro Max - Excellent Condition",
                    "description": "Selling my iPhone 14 Pro Max in excellent condition. Used for only 6 months. Comes with original box, charger, and screen protector already applied. No scratches or dents. Battery health at 98%.",
                            "category": "Smartphones",
                                    "price": 850000,
                                            "location": "Dar es Salaam",
                                                    "condition": "Used",
                                                            "contact_phone": "+255712345678",
                                                                    "contact_email": "<EMAIL>"
                                                                        },
                                                                            {
                                                                                    "title": "Toyota Corolla 2018 - Low Mileage",
                                                                                            "description": "Well maintained Toyota Corolla 2018 with only 45,000 km on the odometer. Regular service history available. Non-smoking owner. Perfect for daily commuting.",
                                                                                                    "category": "Cars",
                                                                                                            "price": 28000000,
                                                                                                                    "location": "Arusha",
                                                                                                                            "condition": "Used",
                                                                                                                                    "contact_phone": "+255754987654",
                                                                                                                                            "contact_email": "<EMAIL>"
                                                                                                                                                },
                                                                                                                                                    {
                                                                                                                                                            "title": "3 Bedroom House for Sale - Mikocheni",
                                                                                                                                                                    "description": "Beautiful 3 bedroom house in Mikocheni area. Modern kitchen, spacious living room, 2 bathrooms, and a small garden. Close to schools and shopping centers.",
                                                                                                                                                                            "category": "Houses",
                                                                                                                                                                                    "price": 180000000,
                                                                                                                                                                                            "location": "Dar es Salaam",
                                                                                                                                                                                                    "condition": "Used",
                                                                                                                                                                                                            "contact_phone": "+255765432109",
                                                                                                                                                                                                                    "contact_email": "<EMAIL>"
                                                                                                                                                                                                                        },
                                                                                                                                                                                                                            {
                                                                                                                                                                                                                                    "title": "MacBook Pro M2 - Perfect for Students",
                                                                                                                                                                                                                                            "description": "MacBook Pro with M2 chip, 16GB RAM, 512GB SSD. Perfect for students and professionals. Comes with laptop bag and wireless mouse. Still under warranty.",
                                                                                                                                                                                                                                                    "category": "Laptops & Computers",
                                                                                                                                                                                                                                                            "price": 2800000,
                                                                                                                                                                                                                                                                    "location": "Mwanza",
                                                                                                                                                                                                                                                                            "condition": "Used",
                                                                                                                                                                                                                                                                                    "contact_phone": "+255678123456",
                                                                                                                                                                                                                                                                                            "contact_email": "<EMAIL>"
                                                                                                                                                                                                                                                                                                },
                                                                                                                                                                                                                                                                                                    {
                                                                                                                                                                                                                                                                                                            "title": "Modern Sofa Set - 7 Seater",
                                                                                                                                                                                                                                                                                                                    "description": "Beautiful modern sofa set with 7 seats. Comfortable and stylish, perfect for living room. Excellent condition, no pets, no smoking household.",
                                                                                                                                                                                                                                                                                                                            "category": "Furniture",
                                                                                                                                                                                                                                                                                                                                    "price": 850000,
                                                                                                                                                                                                                                                                                                                                            "location": "Dodoma",
                                                                                                                                                                                                                                                                                                                                                    "condition": "Used",
                                                                                                                                                                                                                                                                                                                                                            "contact_phone": "+255689765432",
                                                                                                                                                                                                                                                                                                                                                                    "contact_email": "<EMAIL>"
                                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                                        ]
2025-06-20 10:50:25,758 INFO ipython created_count = 0
2025-06-20 10:50:25,759 INFO ipython for data in listings_data:
        try:
                    listing = frappe.new_doc("Listing")
                            listing.update(data)
2025-06-20 10:50:25,759 INFO ipython         listing.status = "Active"
2025-06-20 10:50:25,759 INFO ipython         listing.listing_type = "For Sale"
2025-06-20 10:50:25,759 INFO ipython         listing.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:50:25,759 INFO ipython         listing.insert(ignore_permissions=True)
2025-06-20 10:50:25,760 INFO ipython         created_count += 1
2025-06-20 10:50:25,760 INFO ipython         print(f"Created: {listing.name} - {listing.title}")
2025-06-20 10:50:25,760 INFO ipython     except Exception as e:
            print(f"Error creating listing '{data['title']}': {str(e)}")
2025-06-20 10:50:25,760 INFO ipython print(f"\nCreated {created_count} listings successfully!")
2025-06-20 10:50:25,760 INFO ipython # Test the API
2025-06-20 10:50:25,760 INFO ipython response = frappe.call("dmsoko.api.listings.get_listings", page=1, page_size=10)
2025-06-20 10:50:25,760 INFO ipython print(f"API Response: Found {len(response['data']['listings'])} listings")
2025-06-20 10:50:25,760 INFO ipython === session end ===
2025-06-20 10:50:55,018 INFO ipython === bench console session ===
2025-06-20 10:50:55,018 INFO ipython import frappe
2025-06-20 10:50:55,018 INFO ipython # Create first listing
2025-06-20 10:50:55,018 INFO ipython listing1 = frappe.new_doc("Listing")
2025-06-20 10:50:55,018 INFO ipython listing1.title = "iPhone 14 Pro Max - Excellent Condition"
2025-06-20 10:50:55,019 INFO ipython listing1.description = "Selling my iPhone 14 Pro Max in excellent condition. Used for only 6 months."
2025-06-20 10:50:55,019 INFO ipython listing1.category = "Smartphones"
2025-06-20 10:50:55,019 INFO ipython listing1.price = 850000
2025-06-20 10:50:55,019 INFO ipython listing1.location = "Dar es Salaam"
2025-06-20 10:50:55,019 INFO ipython listing1.condition = "Used"
2025-06-20 10:50:55,019 INFO ipython listing1.contact_phone = "+255712345678"
2025-06-20 10:50:55,019 INFO ipython listing1.contact_email = "<EMAIL>"
2025-06-20 10:50:55,019 INFO ipython listing1.status = "Active"
2025-06-20 10:50:55,020 INFO ipython listing1.listing_type = "For Sale"
2025-06-20 10:50:55,020 INFO ipython listing1.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:50:55,020 INFO ipython listing1.insert(ignore_permissions=True)
2025-06-20 10:50:55,020 INFO ipython print(f"Created: {listing1.name}")
2025-06-20 10:50:55,020 INFO ipython # Create second listing
2025-06-20 10:50:55,020 INFO ipython listing2 = frappe.new_doc("Listing")
2025-06-20 10:50:55,021 INFO ipython listing2.title = "Toyota Corolla 2018 - Low Mileage"
2025-06-20 10:50:55,021 INFO ipython listing2.description = "Well maintained Toyota Corolla 2018 with only 45,000 km on the odometer."
2025-06-20 10:50:55,021 INFO ipython listing2.category = "Cars"
2025-06-20 10:50:55,021 INFO ipython listing2.price = 28000000
2025-06-20 10:50:55,021 INFO ipython listing2.location = "Arusha"
2025-06-20 10:50:55,021 INFO ipython listing2.condition = "Used"
2025-06-20 10:50:55,021 INFO ipython listing2.contact_phone = "+255754987654"
2025-06-20 10:50:55,022 INFO ipython listing2.contact_email = "<EMAIL>"
2025-06-20 10:50:55,022 INFO ipython listing2.status = "Active"
2025-06-20 10:50:55,022 INFO ipython listing2.listing_type = "For Sale"
2025-06-20 10:50:55,022 INFO ipython listing2.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:50:55,022 INFO ipython listing2.insert(ignore_permissions=True)
2025-06-20 10:50:55,022 INFO ipython print(f"Created: {listing2.name}")
2025-06-20 10:50:55,022 INFO ipython # Create third listing
2025-06-20 10:50:55,022 INFO ipython listing3 = frappe.new_doc("Listing")
2025-06-20 10:50:55,022 INFO ipython listing3.title = "3 Bedroom House for Sale - Mikocheni"
2025-06-20 10:50:55,022 INFO ipython listing3.description = "Beautiful 3 bedroom house in Mikocheni area. Modern kitchen, spacious living room."
2025-06-20 10:50:55,023 INFO ipython listing3.category = "Houses"
2025-06-20 10:50:55,023 INFO ipython listing3.price = 180000000
2025-06-20 10:50:55,023 INFO ipython listing3.location = "Dar es Salaam"
2025-06-20 10:50:55,023 INFO ipython listing3.condition = "Used"
2025-06-20 10:50:55,023 INFO ipython listing3.contact_phone = "+255765432109"
2025-06-20 10:50:55,023 INFO ipython listing3.contact_email = "<EMAIL>"
2025-06-20 10:50:55,023 INFO ipython listing3.status = "Active"
2025-06-20 10:50:55,023 INFO ipython listing3.listing_type = "For Sale"
2025-06-20 10:50:55,023 INFO ipython listing3.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:50:55,024 INFO ipython listing3.insert(ignore_permissions=True)
2025-06-20 10:50:55,024 INFO ipython print(f"Created: {listing3.name}")
2025-06-20 10:50:55,024 INFO ipython print("All listings created successfully!")
2025-06-20 10:50:55,024 INFO ipython # Test the API
2025-06-20 10:50:55,024 INFO ipython response = frappe.call("dmsoko.api.listings.get_listings", page=1, page_size=10)
2025-06-20 10:50:55,024 INFO ipython print(f"API Response: Found {len(response['data']['listings'])} listings")
2025-06-20 10:50:55,024 INFO ipython for listing in response['data']['listings']:
        print(f"- {listing['name']}: {listing['title']} - {listing['price']:,} TZS")
        
2025-06-20 10:50:55,024 INFO ipython === session end ===
2025-06-20 10:54:46,115 INFO ipython === bench console session ===
2025-06-20 10:54:46,116 INFO ipython import frappe
2025-06-20 10:54:46,116 INFO ipython # Create listings and commit to database
2025-06-20 10:54:46,116 INFO ipython listings_data = [
    {
            "title": "iPhone 14 Pro Max - Excellent Condition",
                    "description": "Selling my iPhone 14 Pro Max in excellent condition. Used for only 6 months. Comes with original box, charger, and screen protector already applied.",
                            "category": "Smartphones",
                                    "price": 850000,
                                            "location": "Dar es Salaam",
                                                    "condition": "Used",
                                                            "contact_phone": "+255712345678",
                                                                    "contact_email": "<EMAIL>",
                                                                            "status": "Active",
                                                                                    "listing_type": "For Sale"
                                                                                        },
                                                                                            {
                                                                                                    "title": "Toyota Corolla 2018 - Low Mileage",
                                                                                                            "description": "Well maintained Toyota Corolla 2018 with only 45,000 km on the odometer. Regular service history available.",
                                                                                                                    "category": "Cars",
                                                                                                                            "price": 28000000,
                                                                                                                                    "location": "Arusha",
                                                                                                                                            "condition": "Used",
                                                                                                                                                    "contact_phone": "+255754987654",
                                                                                                                                                            "contact_email": "<EMAIL>",
                                                                                                                                                                    "status": "Active",
                                                                                                                                                                            "listing_type": "For Sale"
                                                                                                                                                                                },
                                                                                                                                                                                    {
                                                                                                                                                                                            "title": "3 Bedroom House for Sale - Mikocheni",
                                                                                                                                                                                                    "description": "Beautiful 3 bedroom house in Mikocheni area. Modern kitchen, spacious living room, 2 bathrooms, and a small garden.",
                                                                                                                                                                                                            "category": "Houses",
                                                                                                                                                                                                                    "price": 180000000,
                                                                                                                                                                                                                            "location": "Dar es Salaam",
                                                                                                                                                                                                                                    "condition": "Used",
                                                                                                                                                                                                                                            "contact_phone": "+255765432109",
                                                                                                                                                                                                                                                    "contact_email": "<EMAIL>",
                                                                                                                                                                                                                                                            "status": "Active",
                                                                                                                                                                                                                                                                    "listing_type": "For Sale"
                                                                                                                                                                                                                                                                        },
                                                                                                                                                                                                                                                                            {
                                                                                                                                                                                                                                                                                    "title": "MacBook Pro M2 - Perfect for Students",
                                                                                                                                                                                                                                                                                            "description": "MacBook Pro with M2 chip, 16GB RAM, 512GB SSD. Perfect for students and professionals. Comes with laptop bag.",
                                                                                                                                                                                                                                                                                                    "category": "Laptops & Computers",
                                                                                                                                                                                                                                                                                                            "price": 2800000,
                                                                                                                                                                                                                                                                                                                    "location": "Mwanza",
                                                                                                                                                                                                                                                                                                                            "condition": "Used",
                                                                                                                                                                                                                                                                                                                                    "contact_phone": "+255678123456",
                                                                                                                                                                                                                                                                                                                                            "contact_email": "<EMAIL>",
                                                                                                                                                                                                                                                                                                                                                    "status": "Active",
                                                                                                                                                                                                                                                                                                                                                            "listing_type": "For Sale"
                                                                                                                                                                                                                                                                                                                                                                },
                                                                                                                                                                                                                                                                                                                                                                    {
                                                                                                                                                                                                                                                                                                                                                                            "title": "Modern Sofa Set - 7 Seater",
                                                                                                                                                                                                                                                                                                                                                                                    "description": "Beautiful modern sofa set with 7 seats. Comfortable and stylish, perfect for living room. Excellent condition.",
                                                                                                                                                                                                                                                                                                                                                                                            "category": "Furniture",
                                                                                                                                                                                                                                                                                                                                                                                                    "price": 850000,
                                                                                                                                                                                                                                                                                                                                                                                                            "location": "Dodoma",
                                                                                                                                                                                                                                                                                                                                                                                                                    "condition": "Used",
                                                                                                                                                                                                                                                                                                                                                                                                                            "contact_phone": "+255689765432",
                                                                                                                                                                                                                                                                                                                                                                                                                                    "contact_email": "<EMAIL>",
                                                                                                                                                                                                                                                                                                                                                                                                                                            "status": "Active",
                                                                                                                                                                                                                                                                                                                                                                                                                                                    "listing_type": "For Sale"
                                                                                                                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                                                                                                                        ]
2025-06-20 10:54:46,117 INFO ipython created_listings = []
2025-06-20 10:54:46,117 INFO ipython for data in listings_data:
        try:
                    listing = frappe.new_doc("Listing")
                            listing.update(data)
2025-06-20 10:54:46,117 INFO ipython         listing.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:54:46,117 INFO ipython         listing.insert(ignore_permissions=True)
2025-06-20 10:54:46,118 INFO ipython         created_listings.append(listing.name)
2025-06-20 10:54:46,118 INFO ipython         print(f"Created: {listing.name} - {listing.title}")
2025-06-20 10:54:46,118 INFO ipython     except Exception as e:
            print(f"Error creating listing: {str(e)}")
2025-06-20 10:54:46,118 INFO ipython # Commit to database
2025-06-20 10:54:46,118 INFO ipython frappe.db.commit()
2025-06-20 10:54:46,119 INFO ipython print(f"\nCommitted {len(created_listings)} listings to database")
2025-06-20 10:54:46,119 INFO ipython # Verify in database
2025-06-20 10:54:46,119 INFO ipython count = frappe.db.count("Listing")
2025-06-20 10:54:46,119 INFO ipython print(f"Total listings in database: {count}")
2025-06-20 10:54:46,119 INFO ipython === session end ===
2025-06-20 10:55:20,570 INFO ipython === bench console session ===
2025-06-20 10:55:20,571 INFO ipython import frappe
2025-06-20 10:55:20,571 INFO ipython # Create first listing
2025-06-20 10:55:20,571 INFO ipython listing1 = frappe.new_doc("Listing")
2025-06-20 10:55:20,571 INFO ipython listing1.title = "iPhone 14 Pro Max - Excellent Condition"
2025-06-20 10:55:20,571 INFO ipython listing1.description = "Selling my iPhone 14 Pro Max in excellent condition. Used for only 6 months."
2025-06-20 10:55:20,572 INFO ipython listing1.category = "Smartphones"
2025-06-20 10:55:20,572 INFO ipython listing1.price = 850000
2025-06-20 10:55:20,572 INFO ipython listing1.location = "Dar es Salaam"
2025-06-20 10:55:20,572 INFO ipython listing1.condition = "Used"
2025-06-20 10:55:20,572 INFO ipython listing1.contact_phone = "+255712345678"
2025-06-20 10:55:20,572 INFO ipython listing1.contact_email = "<EMAIL>"
2025-06-20 10:55:20,573 INFO ipython listing1.status = "Active"
2025-06-20 10:55:20,573 INFO ipython listing1.listing_type = "For Sale"
2025-06-20 10:55:20,573 INFO ipython listing1.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:55:20,573 INFO ipython listing1.insert(ignore_permissions=True)
2025-06-20 10:55:20,573 INFO ipython print(f"Created: {listing1.name}")
2025-06-20 10:55:20,573 INFO ipython # Create second listing
2025-06-20 10:55:20,573 INFO ipython listing2 = frappe.new_doc("Listing")
2025-06-20 10:55:20,573 INFO ipython listing2.title = "Toyota Corolla 2018 - Low Mileage"
2025-06-20 10:55:20,574 INFO ipython listing2.description = "Well maintained Toyota Corolla 2018 with only 45,000 km on the odometer."
2025-06-20 10:55:20,574 INFO ipython listing2.category = "Cars"
2025-06-20 10:55:20,574 INFO ipython listing2.price = 28000000
2025-06-20 10:55:20,574 INFO ipython listing2.location = "Arusha"
2025-06-20 10:55:20,574 INFO ipython listing2.condition = "Used"
2025-06-20 10:55:20,574 INFO ipython listing2.contact_phone = "+255754987654"
2025-06-20 10:55:20,574 INFO ipython listing2.contact_email = "<EMAIL>"
2025-06-20 10:55:20,574 INFO ipython listing2.status = "Active"
2025-06-20 10:55:20,575 INFO ipython listing2.listing_type = "For Sale"
2025-06-20 10:55:20,575 INFO ipython listing2.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:55:20,575 INFO ipython listing2.insert(ignore_permissions=True)
2025-06-20 10:55:20,575 INFO ipython print(f"Created: {listing2.name}")
2025-06-20 10:55:20,575 INFO ipython # Create third listing
2025-06-20 10:55:20,575 INFO ipython listing3 = frappe.new_doc("Listing")
2025-06-20 10:55:20,575 INFO ipython listing3.title = "3 Bedroom House for Sale - Mikocheni"
2025-06-20 10:55:20,575 INFO ipython listing3.description = "Beautiful 3 bedroom house in Mikocheni area. Modern kitchen, spacious living room."
2025-06-20 10:55:20,575 INFO ipython listing3.category = "Houses"
2025-06-20 10:55:20,576 INFO ipython listing3.price = 180000000
2025-06-20 10:55:20,576 INFO ipython listing3.location = "Dar es Salaam"
2025-06-20 10:55:20,576 INFO ipython listing3.condition = "Used"
2025-06-20 10:55:20,576 INFO ipython listing3.contact_phone = "+255765432109"
2025-06-20 10:55:20,576 INFO ipython listing3.contact_email = "<EMAIL>"
2025-06-20 10:55:20,576 INFO ipython listing3.status = "Active"
2025-06-20 10:55:20,576 INFO ipython listing3.listing_type = "For Sale"
2025-06-20 10:55:20,576 INFO ipython listing3.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:55:20,576 INFO ipython listing3.insert(ignore_permissions=True)
2025-06-20 10:55:20,577 INFO ipython print(f"Created: {listing3.name}")
2025-06-20 10:55:20,577 INFO ipython # Commit to database
2025-06-20 10:55:20,577 INFO ipython frappe.db.commit()
2025-06-20 10:55:20,577 INFO ipython print("Committed to database")
2025-06-20 10:55:20,577 INFO ipython # Verify
2025-06-20 10:55:20,577 INFO ipython count = frappe.db.count("Listing")
2025-06-20 10:55:20,577 INFO ipython print(f"Total listings: {count}")
2025-06-20 10:55:20,577 INFO ipython === session end ===
2025-06-20 10:57:17,978 INFO ipython === bench console session ===
2025-06-20 10:57:17,979 INFO ipython import frappe
2025-06-20 10:57:17,979 INFO ipython # Create more listings for better display
2025-06-20 10:57:17,979 INFO ipython more_listings = [
    {
            "title": "Samsung Galaxy S23 Ultra - Like New",
                    "description": "Samsung Galaxy S23 Ultra in excellent condition. 256GB storage, comes with case and screen protector.",
                            "category": "Smartphones",
                                    "price": 1200000,
                                            "location": "Mwanza",
                                                    "condition": "Used",
                                                            "contact_phone": "+255678987654",
                                                                    "contact_email": "<EMAIL>"
                                                                        },
                                                                            {
                                                                                    "title": "Honda Civic 2020 - Automatic",
                                                                                            "description": "Honda Civic 2020 automatic transmission. Well maintained, low mileage, perfect for city driving.",
                                                                                                    "category": "Cars",
                                                                                                            "price": 35000000,
                                                                                                                    "location": "Dar es Salaam",
                                                                                                                            "condition": "Used",
                                                                                                                                    "contact_phone": "+255712987654",
                                                                                                                                            "contact_email": "<EMAIL>"
                                                                                                                                                },
                                                                                                                                                    {
                                                                                                                                                            "title": "Gaming Laptop - ASUS ROG",
                                                                                                                                                                    "description": "ASUS ROG gaming laptop with RTX 3060, 16GB RAM, 1TB SSD. Perfect for gaming and professional work.",
                                                                                                                                                                            "category": "Laptops & Computers",
                                                                                                                                                                                    "price": 3500000,
                                                                                                                                                                                            "location": "Arusha",
                                                                                                                                                                                                    "condition": "Used",
                                                                                                                                                                                                            "contact_phone": "+255754123456",
                                                                                                                                                                                                                    "contact_email": "<EMAIL>"
                                                                                                                                                                                                                        },
                                                                                                                                                                                                                            {
                                                                                                                                                                                                                                    "title": "Dining Table Set - 6 Chairs",
                                                                                                                                                                                                                                            "description": "Beautiful wooden dining table with 6 matching chairs. Perfect for family dining room.",
                                                                                                                                                                                                                                                    "category": "Furniture",
                                                                                                                                                                                                                                                            "price": 650000,
                                                                                                                                                                                                                                                                    "location": "Mbeya",
                                                                                                                                                                                                                                                                            "condition": "Used",
                                                                                                                                                                                                                                                                                    "contact_phone": "+255689123456",
                                                                                                                                                                                                                                                                                            "contact_email": "<EMAIL>"
                                                                                                                                                                                                                                                                                                },
                                                                                                                                                                                                                                                                                                    {
                                                                                                                                                                                                                                                                                                            "title": "2 Bedroom Apartment - Kinondoni",
                                                                                                                                                                                                                                                                                                                    "description": "Modern 2 bedroom apartment in Kinondoni. Fully furnished, great location near amenities.",
                                                                                                                                                                                                                                                                                                                            "category": "Apartments",
                                                                                                                                                                                                                                                                                                                                    "price": 85000000,
                                                                                                                                                                                                                                                                                                                                            "location": "Dar es Salaam",
                                                                                                                                                                                                                                                                                                                                                    "condition": "Used",
                                                                                                                                                                                                                                                                                                                                                            "contact_phone": "+255765987654",
                                                                                                                                                                                                                                                                                                                                                                    "contact_email": "<EMAIL>"
                                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                                        ]
2025-06-20 10:57:17,979 INFO ipython count = 0
2025-06-20 10:57:17,980 INFO ipython for data in more_listings:
        try:
                    listing = frappe.new_doc("Listing")
                            listing.title = data["title"]
2025-06-20 10:57:17,980 INFO ipython         listing.description = data["description"]
2025-06-20 10:57:17,980 INFO ipython         listing.category = data["category"]
2025-06-20 10:57:17,980 INFO ipython         listing.price = data["price"]
2025-06-20 10:57:17,980 INFO ipython         listing.location = data["location"]
2025-06-20 10:57:17,981 INFO ipython         listing.condition = data["condition"]
2025-06-20 10:57:17,981 INFO ipython         listing.contact_phone = data["contact_phone"]
2025-06-20 10:57:17,981 INFO ipython         listing.contact_email = data["contact_email"]
2025-06-20 10:57:17,981 INFO ipython         listing.status = "Active"
2025-06-20 10:57:17,981 INFO ipython         listing.listing_type = "For Sale"
2025-06-20 10:57:17,982 INFO ipython         listing.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:57:17,982 INFO ipython         listing.insert(ignore_permissions=True)
2025-06-20 10:57:17,982 INFO ipython         count += 1
2025-06-20 10:57:17,982 INFO ipython         print(f"Created: {listing.name} - {listing.title}")
2025-06-20 10:57:17,983 INFO ipython     except Exception as e:
            print(f"Error: {str(e)}")
2025-06-20 10:57:17,983 INFO ipython frappe.db.commit()
2025-06-20 10:57:17,983 INFO ipython print(f"\nCreated {count} additional listings")
2025-06-20 10:57:17,983 INFO ipython # Check total
2025-06-20 10:57:17,983 INFO ipython total = frappe.db.count("Listing")
2025-06-20 10:57:17,984 INFO ipython print(f"Total listings in database: {total}")
2025-06-20 10:57:17,984 INFO ipython === session end ===
2025-06-20 10:57:46,013 INFO ipython === bench console session ===
2025-06-20 10:57:46,014 INFO ipython import frappe
2025-06-20 10:57:46,014 INFO ipython # Create Samsung listing
2025-06-20 10:57:46,014 INFO ipython listing4 = frappe.new_doc("Listing")
2025-06-20 10:57:46,015 INFO ipython listing4.title = "Samsung Galaxy S23 Ultra - Like New"
2025-06-20 10:57:46,015 INFO ipython listing4.description = "Samsung Galaxy S23 Ultra in excellent condition. 256GB storage, comes with case and screen protector."
2025-06-20 10:57:46,015 INFO ipython listing4.category = "Smartphones"
2025-06-20 10:57:46,015 INFO ipython listing4.price = 1200000
2025-06-20 10:57:46,015 INFO ipython listing4.location = "Mwanza"
2025-06-20 10:57:46,015 INFO ipython listing4.condition = "Used"
2025-06-20 10:57:46,016 INFO ipython listing4.contact_phone = "+255678987654"
2025-06-20 10:57:46,016 INFO ipython listing4.contact_email = "<EMAIL>"
2025-06-20 10:57:46,016 INFO ipython listing4.status = "Active"
2025-06-20 10:57:46,016 INFO ipython listing4.listing_type = "For Sale"
2025-06-20 10:57:46,016 INFO ipython listing4.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:57:46,016 INFO ipython listing4.insert(ignore_permissions=True)
2025-06-20 10:57:46,016 INFO ipython print(f"Created: {listing4.name}")
2025-06-20 10:57:46,017 INFO ipython # Create Honda listing
2025-06-20 10:57:46,017 INFO ipython listing5 = frappe.new_doc("Listing")
2025-06-20 10:57:46,017 INFO ipython listing5.title = "Honda Civic 2020 - Automatic"
2025-06-20 10:57:46,017 INFO ipython listing5.description = "Honda Civic 2020 automatic transmission. Well maintained, low mileage, perfect for city driving."
2025-06-20 10:57:46,017 INFO ipython listing5.category = "Cars"
2025-06-20 10:57:46,017 INFO ipython listing5.price = 35000000
2025-06-20 10:57:46,018 INFO ipython listing5.location = "Dar es Salaam"
2025-06-20 10:57:46,018 INFO ipython listing5.condition = "Used"
2025-06-20 10:57:46,018 INFO ipython listing5.contact_phone = "+255712987654"
2025-06-20 10:57:46,018 INFO ipython listing5.contact_email = "<EMAIL>"
2025-06-20 10:57:46,018 INFO ipython listing5.status = "Active"
2025-06-20 10:57:46,018 INFO ipython listing5.listing_type = "For Sale"
2025-06-20 10:57:46,018 INFO ipython listing5.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:57:46,018 INFO ipython listing5.insert(ignore_permissions=True)
2025-06-20 10:57:46,019 INFO ipython print(f"Created: {listing5.name}")
2025-06-20 10:57:46,019 INFO ipython # Create Gaming Laptop listing
2025-06-20 10:57:46,019 INFO ipython listing6 = frappe.new_doc("Listing")
2025-06-20 10:57:46,019 INFO ipython listing6.title = "Gaming Laptop - ASUS ROG"
2025-06-20 10:57:46,019 INFO ipython listing6.description = "ASUS ROG gaming laptop with RTX 3060, 16GB RAM, 1TB SSD. Perfect for gaming and professional work."
2025-06-20 10:57:46,019 INFO ipython listing6.category = "Laptops & Computers"
2025-06-20 10:57:46,019 INFO ipython listing6.price = 3500000
2025-06-20 10:57:46,020 INFO ipython listing6.location = "Arusha"
2025-06-20 10:57:46,020 INFO ipython listing6.condition = "Used"
2025-06-20 10:57:46,020 INFO ipython listing6.contact_phone = "+255754123456"
2025-06-20 10:57:46,020 INFO ipython listing6.contact_email = "<EMAIL>"
2025-06-20 10:57:46,020 INFO ipython listing6.status = "Active"
2025-06-20 10:57:46,020 INFO ipython listing6.listing_type = "For Sale"
2025-06-20 10:57:46,021 INFO ipython listing6.expires_on = frappe.utils.add_days(frappe.utils.today(), 30)
2025-06-20 10:57:46,021 INFO ipython listing6.insert(ignore_permissions=True)
2025-06-20 10:57:46,021 INFO ipython print(f"Created: {listing6.name}")
2025-06-20 10:57:46,021 INFO ipython frappe.db.commit()
2025-06-20 10:57:46,021 INFO ipython print("Committed to database")
2025-06-20 10:57:46,022 INFO ipython # Check total
2025-06-20 10:57:46,022 INFO ipython total = frappe.db.count("Listing")
2025-06-20 10:57:46,022 INFO ipython print(f"Total listings: {total}")
2025-06-20 10:57:46,022 INFO ipython === session end ===
2025-06-20 11:22:58,816 INFO ipython === bench console session ===
2025-06-20 11:22:58,816 INFO ipython import frappe
2025-06-20 11:22:58,816 INFO ipython # Set the home page to redirect to dmsoko
2025-06-20 11:22:58,817 INFO ipython frappe.db.set_value("Website Settings", "Website Settings", "home_page", "dmsoko")
2025-06-20 11:22:58,817 INFO ipython frappe.db.commit()
2025-06-20 11:22:58,817 INFO ipython print("Home page set to dmsoko")
2025-06-20 11:22:58,817 INFO ipython === session end ===
2025-06-20 11:42:51,294 INFO ipython === bench console session ===
2025-06-20 11:42:51,295 INFO ipython import frappe
2025-06-20 11:42:51,295 INFO ipython # Check if site is in production mode
2025-06-20 11:42:51,296 INFO ipython print("=== SITE CONFIGURATION ===")
2025-06-20 11:42:51,296 INFO ipython print(f"Site: {frappe.local.site}")
2025-06-20 11:42:51,296 INFO ipython print(f"Developer mode: {frappe.conf.get('developer_mode', False)}")
2025-06-20 11:42:51,296 INFO ipython print(f"Debug mode: {frappe.conf.get('debug', False)}")
2025-06-20 11:42:51,296 INFO ipython print(f"Auto reload: {frappe.conf.get('auto_reload', False)}")
2025-06-20 11:42:51,297 INFO ipython # Check system settings
2025-06-20 11:42:51,297 INFO ipython system_settings = frappe.get_single("System Settings")
2025-06-20 11:42:51,297 INFO ipython print(f"Enable scheduler: {system_settings.enable_scheduler}")
2025-06-20 11:42:51,297 INFO ipython print(f"Disable website caching: {system_settings.disable_website_cache}")
2025-06-20 11:42:51,298 INFO ipython # Check if we're using production server
2025-06-20 11:42:51,298 INFO ipython print(f"Server software: {frappe.conf.get('server_software', 'Development')}")
2025-06-20 11:42:51,298 INFO ipython print(f"Environment: {'Production' if not frappe.conf.get('developer_mode') else 'Development'}")
2025-06-20 11:42:51,298 INFO ipython === session end ===
2025-06-20 17:32:24,202 INFO ipython === bench console session ===
2025-06-20 17:32:24,202 INFO ipython import frappe
2025-06-20 17:32:24,202 INFO ipython # Create custom roles
2025-06-20 17:32:24,203 INFO ipython roles = [
    {
            "doctype": "Role",
                    "role_name": "DMSoko User",
                            "desk_access": 0,
                                    "is_custom": 1,
                                            "disabled": 0
                                                },
                                                    {
                                                            "doctype": "Role", 
                                                                    "role_name": "DMSoko Admin",
                                                                            "desk_access": 1,
                                                                                    "is_custom": 1,
                                                                                            "disabled": 0
                                                                                                },
                                                                                                    {
                                                                                                            "doctype": "Role",
                                                                                                                    "role_name": "DMSoko Moderator", 
                                                                                                                            "desk_access": 1,
                                                                                                                                    "is_custom": 1,
                                                                                                                                            "disabled": 0
                                                                                                                                                }
                                                                                                                                                ]
2025-06-20 17:32:24,203 INFO ipython for role_data in roles:
        if not frappe.db.exists("Role", role_data["role_name"]):
                    role = frappe.get_doc(role_data)
                            role.insert()
2025-06-20 17:32:24,203 INFO ipython         print(f"Created role: {role_data['role_name']}")
2025-06-20 17:32:24,203 INFO ipython     else:
            print(f"Role already exists: {role_data['role_name']}")
2025-06-20 17:32:24,203 INFO ipython frappe.db.commit()
2025-06-20 17:32:24,203 INFO ipython # Create roles properly
2025-06-20 17:32:24,203 INFO ipython for role_data in roles:
        if not frappe.db.exists("Role", role_data["role_name"]):
                    role = frappe.get_doc(role_data)
                            role.insert()
2025-06-20 17:32:24,203 INFO ipython         print(f"Created role: {role_data['role_name']}")
2025-06-20 17:32:24,203 INFO ipython     else:
            print(f"Role already exists: {role_data['role_name']}")
2025-06-20 17:32:24,203 INFO ipython frappe.db.commit()
2025-06-20 17:32:24,203 INFO ipython === session end ===
2025-06-20 17:34:29,103 INFO ipython === bench console session ===
2025-06-20 17:34:29,103 INFO ipython exec(open('setup_fixtures.py').read())
2025-06-20 17:34:29,103 INFO ipython exec(open('/home/<USER>/dev/personal/setup_fixtures.py').read())
2025-06-20 17:34:29,103 INFO ipython === session end ===
2025-06-20 17:36:14,055 INFO ipython === bench console session ===
2025-06-20 17:36:14,055 INFO ipython exec(open('/home/<USER>/dev/personal/create_categories.py').read())
2025-06-20 17:36:14,059 INFO ipython === session end ===
2025-06-23 19:09:01,923 INFO ipython === bench console session ===
2025-06-23 19:09:01,923 INFO ipython # Create a test blog post
2025-06-23 19:09:01,924 INFO ipython blog_post = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "Welcome to DMSOKO Blog",
            "author": "DMSOKO Team",
                "blog_intro": "Welcome to our new blog where we share insights about classified listings and marketplace trends.",
                    "content": "This is our first blog post on the DMSOKO platform. We're excited to share valuable content with our community about buying, selling, and trading in the digital marketplace.",
                        "published": 1,
                            "featured": 1,
                                "published_on": "2025-06-23"
                                })
2025-06-23 19:09:01,924 INFO ipython blog_post.insert()
2025-06-23 19:09:01,924 INFO ipython print(f"Created blog post: {blog_post.name}")
2025-06-23 19:09:01,924 INFO ipython # Create another blog post
2025-06-23 19:09:01,924 INFO ipython blog_post2 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "Tips for Selling Your Items Online",
            "author": "DMSOKO Team",
                "blog_intro": "Learn the best practices for creating compelling listings that attract buyers.",
                    "content": "Selling items online can be challenging, but with the right approach, you can maximize your success. Here are some key tips: 1. Take high-quality photos, 2. Write detailed descriptions, 3. Price competitively, 4. Respond quickly to inquiries.",
                        "published": 1,
                            "featured": 0,
                                "published_on": "2025-06-22"
                                })
2025-06-23 19:09:01,924 INFO ipython blog_post2.insert()
2025-06-23 19:09:01,925 INFO ipython print(f"Created blog post: {blog_post2.name}")
2025-06-23 19:09:01,925 INFO ipython # Create a third blog post
2025-06-23 19:09:01,925 INFO ipython blog_post3 = frappe.get_doc({
    "doctype": "Blog Post",
        "title": "How to Stay Safe While Trading Online",
            "author": "DMSOKO Team",
                "blog_intro": "Safety tips for buyers and sellers in online marketplaces.",
                    "content": "Online trading requires caution and awareness. Always meet in public places, verify the identity of the other party, use secure payment methods, and trust your instincts. If something feels wrong, don't proceed with the transaction.",
                        "published": 1,
                            "featured": 1,
                                "published_on": "2025-06-21"
                                })
2025-06-23 19:09:01,925 INFO ipython blog_post3.insert()
2025-06-23 19:09:01,925 INFO ipython print(f"Created blog post: {blog_post3.name}")
2025-06-23 19:09:01,925 INFO ipython === session end ===
2025-06-23 19:09:17,301 INFO ipython === bench console session ===
2025-06-23 19:09:17,302 INFO ipython # Test the blog API
2025-06-23 19:09:17,302 INFO ipython from dmsoko.api.blog import get_featured_blog_posts, get_blog_posts
2025-06-23 19:09:17,302 INFO ipython # Test featured blog posts
2025-06-23 19:09:17,302 INFO ipython featured_result = get_featured_blog_posts(limit=3)
2025-06-23 19:09:17,303 INFO ipython print("Featured blog posts:")
2025-06-23 19:09:17,303 INFO ipython print(featured_result)
2025-06-23 19:09:17,303 INFO ipython print("\n" + "="*50 + "\n")
2025-06-23 19:09:17,303 INFO ipython # Test all blog posts
2025-06-23 19:09:17,303 INFO ipython all_posts_result = get_blog_posts(limit=5, page=1)
2025-06-23 19:09:17,303 INFO ipython print("All blog posts:")
2025-06-23 19:09:17,303 INFO ipython print(all_posts_result)
2025-06-23 19:09:17,304 INFO ipython === session end ===
2025-06-23 19:09:31,229 INFO ipython === bench console session ===
2025-06-23 19:09:31,230 INFO ipython # Check if blog posts exist
2025-06-23 19:09:31,230 INFO ipython blog_posts = frappe.get_all("Blog Post", fields=["name", "title", "published", "featured"])
2025-06-23 19:09:31,230 INFO ipython print("Blog posts in database:")
2025-06-23 19:09:31,230 INFO ipython for post in blog_posts:
        print(f"- {post.name}: {post.title} (published: {post.published}, featured: {post.featured})")
        
2025-06-23 19:09:31,230 INFO ipython # Check the DocType name
2025-06-23 19:09:31,231 INFO ipython print(f"\nDocType exists: {frappe.db.exists('DocType', 'Blog Post')}")
2025-06-23 19:09:31,231 INFO ipython === session end ===
2025-06-23 19:09:50,041 INFO ipython === bench console session ===
2025-06-23 19:09:50,042 INFO ipython # Check which Blog Post DocType is being used
2025-06-23 19:09:50,042 INFO ipython blog_post_doctype = frappe.get_doc("DocType", "Blog Post")
2025-06-23 19:09:50,042 INFO ipython print(f"Blog Post DocType module: {blog_post_doctype.module}")
2025-06-23 19:09:50,042 INFO ipython print(f"Blog Post DocType app: {blog_post_doctype.app}")
2025-06-23 19:09:50,043 INFO ipython # Check if our custom blog posts exist in a different table
2025-06-23 19:09:50,043 INFO ipython import frappe.db
2025-06-23 19:09:50,043 INFO ipython tables = frappe.db.sql("SHOW TABLES LIKE '%blog%'", as_dict=True)
2025-06-23 19:09:50,043 INFO ipython print(f"\nTables with 'blog' in name:")
2025-06-23 19:09:50,043 INFO ipython for table in tables:
        print(f"- {table}")
        
2025-06-23 19:09:50,044 INFO ipython === session end ===
